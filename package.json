{"name": "anatomy-land-monorepo", "private": true, "version": "1.0.0", "type": "module", "workspaces": ["apps/frontend", "apps/backend"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "npm run dev --workspace=apps/frontend", "dev:backend": "npm run dev --workspace=apps/backend", "build": "npm run build --workspace=apps/frontend", "build:frontend": "npm run build --workspace=apps/frontend", "build:backend": "npm run build --workspace=apps/backend", "lint": "npm run lint --workspace=apps/frontend && npm run lint --workspace=apps/backend", "preview": "npm run preview --workspace=apps/frontend"}, "devDependencies": {"concurrently": "^8.2.2"}}