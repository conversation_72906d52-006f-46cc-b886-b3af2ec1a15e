# Anatomy Land - Monorepo

## Summary

Anatomy Land is a visual memorization tool designed for medical students (MD, PhD, PA, PT) to accelerate learning anatomy coursework. The platform features:

- Interactive map interface mirroring human anatomical regions
- Themed videos with characters and visuals that enhance memorization
- Quizzes and progress tracking to reinforce learning
- Responsive design for desktop and mobile devices

A comprehensive anatomy learning platform built with modern web technologies.

## Project Structure

```
anatomy-land-monorepo/
├── apps/
│   ├── frontend/          # React + Vite frontend application
│   └── backend/           # Fastify backend API server
├── package.json           # Root package.json with workspace configuration
└── README.md
```

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm 9+

### Installation

1. Clone the repository
2. Install dependencies for all workspaces:

```bash
npm install
```

### Development

Start both frontend and backend in development mode:

```bash
npm run dev
```

This will start:
- Frontend: http://localhost:5173
- Backend: http://localhost:3001

### Individual Development

Start only the frontend:
```bash
npm run dev:frontend
```

Start only the backend:
```bash
npm run dev:backend
```

### Building

Build the frontend for production:
```bash
npm run build
```

Build individual applications:
```bash
npm run build:frontend
npm run build:backend
```

## Applications

### Frontend (`apps/frontend`)

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: TailwindCSS
- **Icons**: Lucide React
- **Architecture**: Atomic Design Pattern

### Backend (`apps/backend`)

- **Framework**: Fastify
- **Language**: TypeScript
- **Features**: 
  - CORS enabled
  - Health check endpoint
  - Anatomy regions API
  - User progress tracking

## API Endpoints

- `GET /health` - Health check
- `GET /api/anatomy/regions` - Get all anatomy regions
- `GET /api/anatomy/regions/:regionId` - Get specific region details
- `GET /api/user/progress` - Get user progress data

## Scripts

- `npm run dev` - Start both frontend and backend
- `npm run dev:frontend` - Start frontend only
- `npm run dev:backend` - Start backend only
- `npm run build` - Build frontend
- `npm run build:frontend` - Build frontend
- `npm run build:backend` - Build backend
- `npm run lint` - Lint both applications

## Contributing

1. Make changes in the appropriate workspace (`apps/frontend` or `apps/backend`)
2. Test your changes locally
3. Submit a pull request

## License

Private - All rights reserved.
