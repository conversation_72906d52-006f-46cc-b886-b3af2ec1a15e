import React, { lazy, Suspense } from 'react';
import { Map } from './components/organisms/Map';
import { RegionPage } from './components/organisms/RegionPage';

// Lazy load components for code splitting
const Header = lazy(() => import('./components/organisms/Header').then(module => ({ default: module.Header })));
const HeroSection = lazy(() => import('./components/organisms/HeroSection').then(module => ({ default: module.HeroSection })));
const FeaturesSection = lazy(() => import('./components/organisms/FeaturesSection').then(module => ({ default: module.FeaturesSection })));
const VideoSection = lazy(() => import('./components/organisms/VideoSection').then(module => ({ default: module.VideoSection })));
const Footer = lazy(() => import('./components/organisms/Footer').then(module => ({ default: module.Footer })));

function App() {
  // Toggle this to test logged-in state
  const isLoggedIn = true; // Change to true to test logged-in state
  const userEmail = "<EMAIL>";

  // Simple routing - in a real app you'd use React Router
  const currentPath = window.location.pathname;
  
  if (currentPath === '/map') {
    return <Map />;
  }
  
  // Handle region pages
  if (currentPath.startsWith('/region/')) {
    const regionId = currentPath.split('/region/')[1];
    return <RegionPage regionId={regionId} />;
  }

  return (
    <div className="min-h-screen bg-white">
      <Suspense fallback={<div className="flex items-center justify-center min-h-screen"><div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div></div>}>
        <Header isLoggedIn={isLoggedIn} userEmail={userEmail} />
        <main>
          <HeroSection />
          <FeaturesSection />
          <VideoSection />
        </main>
        <Footer />
      </Suspense>
    </div>
  );
}

export default App;