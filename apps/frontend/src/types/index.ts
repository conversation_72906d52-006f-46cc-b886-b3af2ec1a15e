export interface NavigationItem {
  label: string;
  href: string;
  hasDropdown?: boolean;
}

export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  image: string;
  imageAlt: string;
}

export interface FooterSection {
  title: string;
  links: Array<{
    label: string;
    href: string;
  }>;
}

export interface SocialLink {
  name: string;
  href: string;
  icon: string;
}

export interface DropdownItem {
  label: string;
  href: string;
  description?: string;
}