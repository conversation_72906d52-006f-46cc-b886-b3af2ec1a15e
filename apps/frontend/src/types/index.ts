export interface NavigationItem {
  label: string;
  href: string;
  hasDropdown?: boolean;
}

export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  image: string;
  imageAlt: string;
}

export interface FooterSection {
  title: string;
  links: Array<{
    label: string;
    href: string;
  }>;
}

export interface SocialLink {
  name: string;
  href: string;
  icon: string;
}

export interface DropdownItem {
  label: string;
  href: string;
  description?: string;
}

// Learning Content Types
export interface Episode {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  duration: number; // in seconds
  videoUrl?: string;
  vimeoId?: string;
  isCompleted: boolean;
  completionPercentage: number;
  isCurrentlyPlaying?: boolean;
}

export interface Quiz {
  id: string;
  episodeId: string;
  title: string;
  description?: string;
  questions: QuizQuestion[];
  isCompleted: boolean;
  isUnlocked: boolean;
  score?: number;
  attempts: number;
  maxAttempts?: number;
}

export interface QuizQuestion {
  id: string;
  type: 'multiple-choice' | 'drag-drop' | 'labeling';
  question: string;
  options?: string[];
  correctAnswer: string | string[];
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface MemorizationCard {
  id: string;
  lessonId: string;
  title: string;
  description: string;
  thingLinkUrl: string;
  isUnlocked: boolean;
  completedAt?: string;
}

export interface Lesson {
  id: string;
  regionId: string;
  title: string;
  description: string;
  thumbnail: string;
  episodes: Episode[];
  quizzes: Quiz[];
  memorizationCard?: MemorizationCard;
  totalDuration: number;
  episodeCount: number;
  completionRate: number;
  isCompleted: boolean;
  status: LessonStatus;
}

export interface LessonStatus {
  allEpisodesCompleted: boolean;
  allQuizzesCompleted: boolean;
  memorizationCardUnlocked: boolean;
  memorizationCardCompleted: boolean;
  masteryAchieved: boolean;
  lastAccessedAt?: string;
}

export interface UserProgress {
  userId: string;
  lessonId: string;
  episodeProgress: Record<string, EpisodeProgress>;
  quizProgress: Record<string, QuizProgress>;
  memorizationCardProgress?: MemorizationCardProgress;
  overallCompletionPercentage: number;
  lastUpdated: string;
}

export interface EpisodeProgress {
  episodeId: string;
  watchedDuration: number;
  totalDuration: number;
  completionPercentage: number;
  isCompleted: boolean;
  completedAt?: string;
  lastWatchedAt: string;
}

export interface QuizProgress {
  quizId: string;
  isCompleted: boolean;
  score: number;
  attempts: number;
  completedAt?: string;
  answers: Record<string, string | string[]>;
}

export interface MemorizationCardProgress {
  cardId: string;
  isCompleted: boolean;
  completedAt?: string;
  timeSpent: number; // in seconds
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface LessonApiResponse extends ApiResponse<Lesson> {}
export interface ProgressApiResponse extends ApiResponse<UserProgress> {}

// Tab Types for Lesson Page
export type LessonTab = 'episodes' | 'quizzes' | 'memory-card';

export interface TabConfig {
  id: LessonTab;
  label: string;
  isEnabled: boolean;
  badge?: number;
}