import React from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

export const Logo: React.FC<LogoProps> = ({ size = 'md', showText = false }) => {
  // Logo dimensions are fixed at 226x70 for all sizes
  const logoWidth = 226;
  const logoHeight = 70;
  
  // Scale factor based on size prop
  const scaleFactors = {
    sm: 0.6,  // ~136x42
    md: 0.8,  // ~181x56
    lg: 1.0   // 226x70 (full size)
  };
  
  const scaleFactor = scaleFactors[size];
  const displayWidth = logoWidth * scaleFactor;
  const displayHeight = logoHeight * scaleFactor;

  return (
    <div className="flex items-center">
      <div className="relative flex-shrink-0">
        <img 
          src="/src/assets/logo.svg" 
          alt="Anatomy Land - Learned. Memorized. Mastered."
          width={displayWidth}
          height={displayHeight}
          style={{
            width: `${displayWidth}px`,
            height: `${displayHeight}px`,
            objectFit: 'contain'
          }}
          className="block"
          onError={(e) => {
            console.error('Logo failed to load:', e);
            // Fallback to text if image fails
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const fallback = target.nextElementSibling as HTMLElement;
            if (fallback) fallback.style.display = 'block';
          }}
        />
        {/* Fallback text logo */}
        <div 
          className="hidden text-blue-600 font-bold text-lg"
          style={{ display: 'none' }}
        >
          ANATOMY LAND
        </div>
      </div>
    </div>
  );
};