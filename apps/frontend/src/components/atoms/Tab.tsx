import React from 'react';
import { cn } from '../../utils/cn';

interface TabProps {
  id: string;
  label: string;
  isActive: boolean;
  isEnabled: boolean;
  badge?: number;
  onClick: (id: string) => void;
  className?: string;
}

export const Tab: React.FC<TabProps> = ({
  id,
  label,
  isActive,
  isEnabled,
  badge,
  onClick,
  className
}) => {
  return (
    <button
      onClick={() => isEnabled && onClick(id)}
      disabled={!isEnabled}
      className={cn(
        "relative px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",
        "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
        isActive && isEnabled
          ? "bg-blue-600 text-white shadow-sm"
          : isEnabled
          ? "text-gray-700 hover:text-blue-600 hover:bg-blue-50"
          : "text-gray-400 cursor-not-allowed",
        className
      )}
    >
      <span className="flex items-center space-x-2">
        <span>{label}</span>
        {badge !== undefined && badge > 0 && (
          <span className={cn(
            "inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded-full",
            isActive && isEnabled
              ? "bg-white text-blue-600"
              : "bg-blue-100 text-blue-600"
          )}>
            {badge}
          </span>
        )}
      </span>
      
      {!isEnabled && (
        <div className="absolute inset-0 bg-gray-100 bg-opacity-50 rounded-lg flex items-center justify-center">
          <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
        </div>
      )}
    </button>
  );
};
