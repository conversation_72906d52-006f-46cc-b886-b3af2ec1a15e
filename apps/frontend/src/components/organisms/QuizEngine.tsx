import React, { useState, useEffect } from 'react';
import { X, CheckCircle, XCircle, ArrowRight, ArrowLeft } from 'lucide-react';
import { cn } from '../../utils/cn';
import { Quiz, QuizQuestion } from '../../types';

interface QuizEngineProps {
  quiz: Quiz;
  onComplete: (results: {
    quizId: string;
    score: number;
    totalQuestions: number;
    percentage: number;
    passed: boolean;
    answers: Record<string, string>;
  }) => void;
  onExit: () => void;
  className?: string;
}

export const QuizEngine: React.FC<QuizEngineProps> = ({
  quiz,
  onComplete,
  onExit,
  className
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showFeedback, setShowFeedback] = useState(false);
  const [isAnswerCorrect, setIsAnswerCorrect] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState<string>('');

  const currentQuestion = quiz.questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === quiz.questions.length - 1;
  const progress = ((currentQuestionIndex + 1) / quiz.questions.length) * 100;

  useEffect(() => {
    // Reset state when question changes
    setShowFeedback(false);
    setSelectedAnswer(answers[currentQuestion?.id] || '');
  }, [currentQuestionIndex, currentQuestion?.id, answers]);

  const handleAnswerSelect = (answer: string) => {
    if (showFeedback) return;
    setSelectedAnswer(answer);
  };

  const handleCheckAnswer = () => {
    if (!selectedAnswer || !currentQuestion) return;

    const correct = selectedAnswer === currentQuestion.correctAnswer;
    setIsAnswerCorrect(correct);
    setShowFeedback(true);
    
    // Save answer
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: selectedAnswer
    }));
  };

  const handleNextQuestion = () => {
    if (isLastQuestion) {
      // Calculate final score and complete quiz
      const finalAnswers = { ...answers, [currentQuestion.id]: selectedAnswer };
      let score = 0;
      
      quiz.questions.forEach(question => {
        if (finalAnswers[question.id] === question.correctAnswer) {
          score++;
        }
      });

      const percentage = Math.round((score / quiz.questions.length) * 100);
      const passed = percentage >= 70; // 70% passing grade

      onComplete({
        quizId: quiz.id,
        score,
        totalQuestions: quiz.questions.length,
        percentage,
        passed,
        answers: finalAnswers
      });
    } else {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  if (!currentQuestion) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No questions available</p>
      </div>
    );
  }

  return (
    <div className={cn("bg-white rounded-xl shadow-lg overflow-hidden", className)}>
      {/* Header */}
      <div className="bg-blue-600 text-white p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">{quiz.title}</h2>
          <button
            onClick={onExit}
            className="text-white hover:text-blue-200 transition-colors duration-200"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
        
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Question {currentQuestionIndex + 1} of {quiz.questions.length}</span>
            <span>{Math.round(progress)}% complete</span>
          </div>
          <div className="w-full bg-blue-500 rounded-full h-2">
            <div 
              className="bg-white h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Question Content */}
      <div className="p-6">
        {/* Question */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {currentQuestion.question}
          </h3>
          
          {/* Difficulty Badge */}
          <div className="mb-4">
            <span className={cn(
              "inline-block px-2 py-1 rounded-full text-xs font-medium",
              currentQuestion.difficulty === 'easy' && "bg-green-100 text-green-800",
              currentQuestion.difficulty === 'medium' && "bg-yellow-100 text-yellow-800",
              currentQuestion.difficulty === 'hard' && "bg-red-100 text-red-800"
            )}>
              {currentQuestion.difficulty}
            </span>
          </div>
        </div>

        {/* Answer Options */}
        {currentQuestion.type === 'multiple-choice' && currentQuestion.options && (
          <div className="space-y-3 mb-6">
            {currentQuestion.options.map((option, index) => (
              <button
                key={index}
                onClick={() => handleAnswerSelect(option)}
                disabled={showFeedback}
                className={cn(
                  "w-full text-left p-4 rounded-lg border-2 transition-all duration-200",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500",
                  selectedAnswer === option
                    ? showFeedback
                      ? isAnswerCorrect
                        ? "border-green-500 bg-green-50 text-green-800"
                        : "border-red-500 bg-red-50 text-red-800"
                      : "border-blue-500 bg-blue-50 text-blue-800"
                    : showFeedback && option === currentQuestion.correctAnswer
                    ? "border-green-500 bg-green-50 text-green-800"
                    : "border-gray-200 hover:border-gray-300 text-gray-700",
                  showFeedback && "cursor-not-allowed"
                )}
              >
                <div className="flex items-center justify-between">
                  <span>{option}</span>
                  {showFeedback && (
                    <>
                      {option === currentQuestion.correctAnswer && (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      )}
                      {selectedAnswer === option && option !== currentQuestion.correctAnswer && (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </>
                  )}
                </div>
              </button>
            ))}
          </div>
        )}

        {/* Feedback */}
        {showFeedback && (
          <div className={cn(
            "p-4 rounded-lg mb-6",
            isAnswerCorrect ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"
          )}>
            <div className="flex items-start space-x-3">
              {isAnswerCorrect ? (
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
              )}
              <div>
                <p className={cn(
                  "font-medium mb-1",
                  isAnswerCorrect ? "text-green-800" : "text-red-800"
                )}>
                  {isAnswerCorrect ? 'Correct!' : 'Incorrect'}
                </p>
                <p className={cn(
                  "text-sm",
                  isAnswerCorrect ? "text-green-700" : "text-red-700"
                )}>
                  {currentQuestion.explanation}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <button
            onClick={handlePreviousQuestion}
            disabled={currentQuestionIndex === 0}
            className={cn(
              "flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors duration-200",
              currentQuestionIndex === 0
                ? "text-gray-400 cursor-not-allowed"
                : "text-gray-600 hover:text-gray-800 hover:bg-gray-100"
            )}
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Previous</span>
          </button>

          <div className="flex space-x-3">
            {!showFeedback ? (
              <button
                onClick={handleCheckAnswer}
                disabled={!selectedAnswer}
                className={cn(
                  "px-6 py-2 rounded-lg font-medium transition-colors duration-200",
                  selectedAnswer
                    ? "bg-blue-600 text-white hover:bg-blue-700"
                    : "bg-gray-200 text-gray-400 cursor-not-allowed"
                )}
              >
                Check Answer
              </button>
            ) : (
              <button
                onClick={handleNextQuestion}
                className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
              >
                <span>{isLastQuestion ? 'Complete Quiz' : 'Next Question'}</span>
                <ArrowRight className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
