import React, { useState } from 'react';
import { Header } from './Header';
import { Footer } from './Footer';

interface MapRegion {
  id: string;
  name: string;
  position: {
    top: string;
    left: string;
  };
  mobilePosition: {
    top: string;
    left: string;
  };
  image?: string;
  isClickable: boolean;
}

const mapRegions: MapRegion[] = [
  {
    id: 'cranial-carnival',
    name: 'Cranial Carnival',
    position: { top: '8%', left: '42%' },
    mobilePosition: { top: '12%', left: '38%' },
    image: '/src/assets/Cranial-Carnival.jpg',
    isClickable: true
  },
  {
    id: 'space-pirate-chest',
    name: 'Space Pirate Chest',
    position: { top: '32%', left: '28%' },
    mobilePosition: { top: '35%', left: '25%' },
    isClickable: true
  },
  {
    id: 'haunted-arm-farm',
    name: 'Haunted Arm Farm',
    position: { top: '32%', left: '58%' },
    mobilePosition: { top: '35%', left: '62%' },
    image: '/src/assets/Haunted-Arm-Farm.jpg',
    isClickable: true
  },
  {
    id: 'gi-manji',
    name: 'GI-Man<PERSON>',
    position: { top: '48%', left: '42%' },
    mobilePosition: { top: '52%', left: '42%' },
    isClickable: true
  },
  {
    id: 'i-crazy-falls',
    name: 'I-Crazy Falls',
    position: { top: '62%', left: '42%' },
    mobilePosition: { top: '68%', left: '42%' },
    isClickable: true
  },
  {
    id: 'roman-leg-ends',
    name: 'Roman LEG-ends',
    position: { top: '78%', left: '42%' },
    mobilePosition: { top: '85%', left: '42%' },
    isClickable: true
  }
];

export const Map: React.FC = () => {
  const [selectedRegion, setSelectedRegion] = useState<string | null>(null);
  const [isLoggedIn] = useState(true); // This should come from your auth context
  const userEmail = "<EMAIL>";

  const handleRegionClick = (regionId: string) => {
    setSelectedRegion(regionId);
    // Navigate to region page
    window.location.href = `/region/${regionId}`;
  };

  const RegionMarker: React.FC<{ region: MapRegion; isMobile: boolean }> = ({ region, isMobile }) => {
    const position = isMobile ? region.mobilePosition : region.position;
    
    return (
      <div
        className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-200 hover:scale-110 z-10 ${
          selectedRegion === region.id ? 'scale-110' : ''
        }`}
        style={{
          top: position.top,
          left: position.left,
        }}
        onClick={() => handleRegionClick(region.id)}
      >
        {/* Region Sign */}
        <div className="relative">
          {/* Sign Post */}
          <div className="w-1 h-6 sm:h-8 bg-amber-800 mx-auto mb-1"></div>
          
          {/* Sign Board */}
          <div className="bg-amber-100 border-2 border-amber-800 rounded-sm px-1.5 sm:px-2 py-0.5 sm:py-1 shadow-md transform -rotate-2 hover:rotate-0 transition-transform duration-200">
            <span className="text-[10px] sm:text-xs font-bold text-amber-900 whitespace-nowrap leading-tight">
              {region.name}
            </span>
          </div>
          
          {/* Hover Effect */}
          <div className="absolute inset-0 bg-yellow-200 opacity-0 hover:opacity-20 rounded-sm transition-opacity duration-200"></div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      <Header isLoggedIn={isLoggedIn} userEmail={userEmail} />
      
      <main className="flex-1">
        {/* Map Section */}
        <section className="relative min-h-screen bg-gradient-to-br from-cyan-400 via-cyan-500 to-blue-500">
          {/* Header with Responsive Images */}
          <div className="relative z-20 pt-6 sm:pt-8 pb-4 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto text-center">
              {/* Desktop Heading Image */}
              <div className="hidden md:block mb-4">
                <img
                  src="/src/assets/desktop-map-heading.jpg"
                  alt="Explore Anatomy Land - Interactive Learning Map"
                  className="mx-auto max-w-full h-auto object-contain"
                  style={{ maxHeight: '120px' }}
                  loading="lazy"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'block';
                  }}
                />
                {/* Fallback text for desktop */}
                <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-2" style={{ display: 'none' }}>
                  EXPLORE ANATOMY LAND
                </h1>
              </div>

              {/* Mobile Heading Image */}
              <div className="md:hidden mb-4">
                <img
                  src="/src/assets/mobile-map-heading.jpg"
                  alt="Explore Anatomy Land - Interactive Learning Map"
                  className="mx-auto max-w-full h-auto object-contain"
                  style={{ maxHeight: '80px' }}
                  loading="lazy"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'block';
                  }}
                />
                {/* Fallback text for mobile */}
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2" style={{ display: 'none' }}>
                  EXPLORE ANATOMY LAND
                </h1>
              </div>

              <p className="text-base sm:text-lg md:text-xl text-gray-800 font-medium">
                Select a region to start your lesson
              </p>
            </div>
          </div>

          {/* Map Container */}
          <div className="relative w-full h-full min-h-[70vh] sm:min-h-[75vh] md:min-h-[80vh] flex items-center justify-center px-2 sm:px-4">
            {/* Base Map Image */}
            <div className="relative w-full max-w-xs sm:max-w-sm md:max-w-2xl lg:max-w-4xl mx-auto">
              <img
                src="/src/assets/map/map.jpg"
                alt="Interactive Anatomy Map showing different body regions as themed locations"
                className="w-full h-auto object-contain drop-shadow-2xl"
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  objectFit: 'contain'
                }}
                loading="lazy"
              />
              
              {/* Desktop Region Markers */}
              <div className="hidden md:block">
                {mapRegions.map((region) => (
                  <RegionMarker
                    key={region.id}
                    region={region}
                    isMobile={false}
                  />
                ))}
              </div>
              
              {/* Mobile Region Markers */}
              <div className="md:hidden">
                {mapRegions.map((region) => (
                  <RegionMarker
                    key={region.id}
                    region={region}
                    isMobile={true}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Decorative Elements - Responsive */}
          <div className="absolute top-16 sm:top-20 left-4 sm:left-10 w-8 sm:w-16 h-8 sm:h-16 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-16 sm:bottom-20 right-4 sm:right-10 w-12 sm:w-24 h-12 sm:h-24 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute top-1/2 left-2 sm:left-5 w-4 sm:w-8 h-4 sm:h-8 bg-white/20 rounded-full blur-lg"></div>
          <div className="absolute top-1/3 right-2 sm:right-5 w-6 sm:w-12 h-6 sm:h-12 bg-white/15 rounded-full blur-lg"></div>
        </section>

        {/* Region Info Panel (appears when region is selected) */}
        {selectedRegion && (
          <section className="bg-white border-t-4 border-blue-500 py-6 sm:py-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
              <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-4 sm:p-6 shadow-lg">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">
                  {mapRegions.find(r => r.id === selectedRegion)?.name}
                </h2>
                <p className="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base">
                  Explore this anatomical region through engaging storytelling and interactive lessons.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                  <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg font-medium transition-colors duration-200 text-sm sm:text-base">
                    Start Learning
                  </button>
                  <button className="border-2 border-gray-300 hover:border-blue-600 text-gray-700 hover:text-blue-600 px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base">
                    View Preview
                  </button>
                </div>
              </div>
            </div>
          </section>
        )}
      </main>
      
      <Footer />
    </div>
  );
};