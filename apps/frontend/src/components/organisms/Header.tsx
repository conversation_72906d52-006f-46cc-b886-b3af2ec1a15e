import React, { useState } from 'react';
import { Menu, ChevronDown } from 'lucide-react';
import { Logo } from '../atoms/Logo';
import { Button } from '../atoms/Button';
import { MobileMenu } from '../molecules/MobileMenu';
import { DesktopDropdown } from '../molecules/DesktopDropdown';
import { NavigationItem, DropdownItem } from '../../types';

const navigationItems: NavigationItem[] = [
  { label: 'LEARN', href: '#learn', hasDropdown: true },
  { label: 'EXPLORE', href: '#explore', hasDropdown: true },
  { label: 'BLOG', href: '#blog' }
];

const learnDropdownItems: DropdownItem[] = [
  { 
    label: 'Full-length lessons', 
    href: '#full-lessons',
    description: 'Complete anatomy courses with detailed explanations'
  },
  { 
    label: 'Mini lessons', 
    href: '#mini-lessons',
    description: 'Quick focused lessons for specific topics'
  },
  { 
    label: 'Course Library', 
    href: '#course-library',
    description: 'Browse all available anatomy courses'
  },
  { 
    label: 'Study Tools', 
    href: '#study-tools',
    description: 'Interactive tools to enhance your learning'
  }
];

const exploreDropdownItems: DropdownItem[] = [
  { 
    label: 'How it works', 
    href: '#how-it-works',
    description: 'Learn about our teaching methodology'
  },
  { 
    label: 'Pricing', 
    href: '#pricing',
    description: 'View subscription plans and pricing'
  },
  { 
    label: 'About', 
    href: '#about',
    description: 'Our mission and team'
  },
  { 
    label: 'Progress Tracking', 
    href: '#progress',
    description: 'Monitor your learning journey'
  }
];

interface HeaderProps {
  isLoggedIn?: boolean;
  userEmail?: string;
}

export const Header: React.FC<HeaderProps> = ({ 
  isLoggedIn = false, 
  userEmail = "<EMAIL>" 
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const handleDropdownToggle = (label: string) => {
    setActiveDropdown(activeDropdown === label ? null : label);
  };

  const closeAllDropdowns = () => {
    setActiveDropdown(null);
  };

  return (
    <>
      <header className="bg-white shadow-sm sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-[102px]">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Logo size="md" />
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              {navigationItems.map((item) => (
                item.hasDropdown ? (
                  <DesktopDropdown
                    key={item.label}
                    label={item.label}
                    items={item.label === 'LEARN' ? learnDropdownItems : exploreDropdownItems}
                    isOpen={activeDropdown === item.label}
                    onToggle={() => handleDropdownToggle(item.label)}
                    onClose={closeAllDropdowns}
                  />
                ) : (
                  <a
                    key={item.label}
                    href={item.href}
                    className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md px-3 py-2"
                  >
                    {item.label}
                  </a>
                )
              ))}
            </nav>

            {/* Desktop Auth/Account Section */}
            <div className="hidden md:flex items-center space-x-3">
              {isLoggedIn ? (
                <div className="relative">
                  <button
                    onClick={() => handleDropdownToggle('MY_ACCOUNT')}
                    className="flex items-center space-x-2 px-3 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    <span className="font-medium">MY ACCOUNT</span>
                    <ChevronDown 
                      className={`h-4 w-4 transition-transform duration-200 ${
                        activeDropdown === 'MY_ACCOUNT' ? 'rotate-180' : ''
                      }`} 
                    />
                  </button>
                  
                  {/* Account Dropdown */}
                  {activeDropdown === 'MY_ACCOUNT' && (
                    <div className="absolute right-0 top-full mt-1 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                      <div className="py-2">
                        <div className="px-4 py-3 border-b border-gray-100">
                          <p className="text-sm text-gray-500">Signed in as</p>
                          <p className="text-sm font-medium text-gray-900 truncate">{userEmail}</p>
                        </div>
                        <a
                          href="#profile"
                          className="block px-4 py-3 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
                        >
                          Profile Settings
                        </a>
                        <a
                          href="#subscription"
                          className="block px-4 py-3 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
                        >
                          Subscription
                        </a>
                        <a
                          href="#progress"
                          className="block px-4 py-3 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
                        >
                          My Progress
                        </a>
                        <div className="border-t border-gray-100">
                          <a
                            href="#logout"
                            className="block px-4 py-3 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 transition-all duration-200"
                          >
                            Sign Out
                          </a>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <>
                  <Button variant="outline" size="sm">
                    LOG IN
                  </Button>
                  <Button variant="secondary" size="sm">
                    SIGN UP
                  </Button>
                </>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={toggleMobileMenu}
              className="md:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label="Toggle mobile menu"
              aria-expanded={isMobileMenuOpen}
            >
              <Menu className="h-6 w-6" />
            </button>
          </div>
        </div>
      </header>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={closeMobileMenu}
        navigationItems={navigationItems}
        isLoggedIn={isLoggedIn}
        userEmail={userEmail}
      />

      {/* Backdrop for desktop dropdowns */}
      {activeDropdown && (
        <div 
          className="fixed inset-0 z-30 hidden md:block"
          onClick={closeAllDropdowns}
        />
      )}
    </>
  );
};