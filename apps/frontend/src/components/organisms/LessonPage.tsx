import React, { useState, useEffect } from 'react';
import { Header } from './Header';
import { Footer } from './Footer';
import { Breadcrumb } from '../molecules/Breadcrumb';
import { Tab } from '../atoms/Tab';
import { EpisodeCarousel } from '../molecules/EpisodeCarousel';
import { VideoPlayer } from '../molecules/VideoPlayer';
import { QuizCard } from '../molecules/QuizCard';
import { QuizEngine } from './QuizEngine';
import { QuizResults } from '../molecules/QuizResults';
import { MemorizationCard } from './MemorizationCard';
import { ProgressTracker } from '../molecules/ProgressTracker';
import { Lesson, LessonTab, Episode, TabConfig, Quiz } from '../../types';

interface LessonPageProps {
  lessonId: string;
  regionId?: string;
  regionName?: string;
  isLoggedIn?: boolean;
  userEmail?: string;
}

export const LessonPage: React.FC<LessonPageProps> = ({
  lessonId,
  regionId = 'cranial-cavity',
  regionName = 'Cranial Cavity',
  isLoggedIn = false,
  userEmail = "<EMAIL>"
}) => {
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<LessonTab>('episodes');
  const [currentEpisode, setCurrentEpisode] = useState<Episode | null>(null);
  const [activeQuiz, setActiveQuiz] = useState<Quiz | null>(null);
  const [quizResults, setQuizResults] = useState<any>(null);
  const [showQuizEngine, setShowQuizEngine] = useState(false);

  // Tab configuration with unlock logic
  const getTabConfig = (lesson: Lesson | null): TabConfig[] => {
    if (!lesson) return [];

    return [
      {
        id: 'episodes',
        label: 'Episodes',
        isEnabled: true,
        badge: lesson.episodes.length
      },
      {
        id: 'quizzes',
        label: 'Quizzes',
        isEnabled: true,
        badge: lesson.quizzes.filter(q => q.isCompleted).length
      },
      {
        id: 'memory-card',
        label: 'Memory Card',
        isEnabled: lesson.status.memorizationCardUnlocked,
        badge: lesson.status.memorizationCardCompleted ? 1 : 0
      }
    ];
  };

  useEffect(() => {
    const fetchLesson = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/lessons/${lessonId}`);
        const data = await response.json();
        
        if (data.success) {
          setLesson(data.data);
          // Set first episode as current if none selected
          if (data.data.episodes.length > 0) {
            setCurrentEpisode(data.data.episodes[0]);
          }
        }
      } catch (error) {
        console.error('Failed to fetch lesson:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLesson();
  }, [lessonId]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId as LessonTab);
  };

  const handleEpisodeClick = (episode: Episode) => {
    setCurrentEpisode(episode);
    setActiveTab('episodes');
  };

  const handleVideoProgress = async (progress: {
    watchedDuration: number;
    totalDuration: number;
    completionPercentage: number;
    isCompleted: boolean;
  }) => {
    if (!currentEpisode) return;

    try {
      await fetch(`/api/episodes/${currentEpisode.id}/progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(progress),
      });
    } catch (error) {
      console.error('Failed to update video progress:', error);
    }
  };

  const handleVideoComplete = async (episodeId: string) => {
    if (!lesson) return;

    // Update local state
    setLesson(prevLesson => {
      if (!prevLesson) return prevLesson;

      const updatedEpisodes = prevLesson.episodes.map(ep =>
        ep.id === episodeId
          ? { ...ep, isCompleted: true, completionPercentage: 100 }
          : ep
      );

      // Update quizzes unlock status based on completed episodes
      const updatedQuizzes = prevLesson.quizzes.map(quiz => {
        const relatedEpisode = updatedEpisodes.find(ep => ep.id === quiz.episodeId);
        return {
          ...quiz,
          isUnlocked: relatedEpisode?.isCompleted || quiz.isUnlocked
        };
      });

      // Check if all episodes and quizzes are completed for memory card unlock
      const allEpisodesCompleted = updatedEpisodes.every(ep => ep.isCompleted);
      const allQuizzesCompleted = updatedQuizzes.every(quiz => quiz.isCompleted);
      const memorizationCardUnlocked = allEpisodesCompleted && allQuizzesCompleted;

      return {
        ...prevLesson,
        episodes: updatedEpisodes,
        quizzes: updatedQuizzes,
        status: {
          ...prevLesson.status,
          allEpisodesCompleted,
          allQuizzesCompleted,
          memorizationCardUnlocked
        }
      };
    });

    console.log(`Episode ${episodeId} completed! Quizzes may now be unlocked.`);
  };

  const handleQuizClick = (quiz: Quiz) => {
    if (!quiz.isUnlocked) return;
    setActiveQuiz(quiz);
    setShowQuizEngine(true);
    setQuizResults(null);
  };

  const handleQuizComplete = async (results: any) => {
    try {
      const response = await fetch(`/api/quizzes/${results.quizId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ answers: results.answers }),
      });

      if (response.ok) {
        // Update lesson state with quiz completion
        setLesson(prevLesson => {
          if (!prevLesson) return prevLesson;

          const updatedQuizzes = prevLesson.quizzes.map(quiz =>
            quiz.id === results.quizId
              ? {
                  ...quiz,
                  isCompleted: true,
                  score: results.score,
                  attempts: quiz.attempts + 1
                }
              : quiz
          );

          // Check if all episodes and quizzes are completed for memory card unlock
          const allEpisodesCompleted = prevLesson.episodes.every(ep => ep.isCompleted);
          const allQuizzesCompleted = updatedQuizzes.every(quiz => quiz.isCompleted);
          const memorizationCardUnlocked = allEpisodesCompleted && allQuizzesCompleted;

          return {
            ...prevLesson,
            quizzes: updatedQuizzes,
            status: {
              ...prevLesson.status,
              allQuizzesCompleted,
              memorizationCardUnlocked
            }
          };
        });

        setQuizResults(results);
        setShowQuizEngine(false);
      }
    } catch (error) {
      console.error('Failed to submit quiz:', error);
    }
  };

  const handleQuizExit = () => {
    setShowQuizEngine(false);
    setActiveQuiz(null);
  };

  const handleQuizResultsContinue = () => {
    setQuizResults(null);
    setActiveQuiz(null);
  };

  const handleQuizRetake = () => {
    setQuizResults(null);
    setShowQuizEngine(true);
  };

  const handleMemoryCardComplete = async (cardId: string, timeSpent: number) => {
    try {
      await fetch(`/api/memorization-cards/${cardId}/progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ timeSpent, isCompleted: true }),
      });

      // Update lesson state
      setLesson(prevLesson => {
        if (!prevLesson || !prevLesson.memorizationCard) return prevLesson;

        return {
          ...prevLesson,
          memorizationCard: {
            ...prevLesson.memorizationCard,
            completedAt: new Date().toISOString()
          },
          status: {
            ...prevLesson.status,
            memorizationCardCompleted: true,
            masteryAchieved: true // Full mastery achieved!
          }
        };
      });

      console.log(`Memory card completed! Lesson mastery achieved.`);
    } catch (error) {
      console.error('Failed to update memory card progress:', error);
    }
  };

  const breadcrumbItems = [
    { label: 'Map', href: '/map' },
    { label: regionName, href: `/region/${regionId}` },
    { label: lesson?.title || 'Loading...', isActive: true }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex flex-col">
        <Header isLoggedIn={isLoggedIn} userEmail={userEmail} />
        <main className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!lesson) {
    return (
      <div className="min-h-screen bg-white flex flex-col">
        <Header isLoggedIn={isLoggedIn} userEmail={userEmail} />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Lesson not found</h2>
            <p className="text-gray-600">The requested lesson could not be loaded.</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const tabConfig = getTabConfig(lesson);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Header isLoggedIn={isLoggedIn} userEmail={userEmail} />
      
      <main className="flex-1">
        {/* Breadcrumb */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <Breadcrumb items={breadcrumbItems} />
          </div>
        </div>

        {/* Lesson Header */}
        <div className="bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  {lesson.title}
                </h1>
                <p className="text-lg text-gray-600 mb-6">
                  {lesson.description}
                </p>
                
                {/* Lesson Stats */}
                <div className="flex flex-wrap items-center space-x-6 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <span className="font-medium">{lesson.episodeCount}</span>
                    <span>episodes</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="font-medium">{Math.floor(lesson.totalDuration / 60)}</span>
                    <span>minutes total</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="font-medium">{lesson.completionRate}%</span>
                    <span>complete</span>
                  </div>
                </div>
              </div>
              
              {/* Lesson Thumbnail */}
              <div className="mt-6 lg:mt-0 lg:ml-8">
                <img
                  src={lesson.thumbnail}
                  alt={lesson.title}
                  className="w-full lg:w-64 h-36 object-cover rounded-xl shadow-sm"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex space-x-1 py-4">
              {tabConfig.map((tab) => (
                <Tab
                  key={tab.id}
                  id={tab.id}
                  label={tab.label}
                  isActive={activeTab === tab.id}
                  isEnabled={tab.isEnabled}
                  badge={tab.badge}
                  onClick={handleTabChange}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Progress Tracker - Always visible */}
            <div className="mb-8">
              <ProgressTracker
                status={lesson.status}
                episodeCount={lesson.episodes.length}
                quizCount={lesson.quizzes.length}
                completedEpisodes={lesson.episodes.filter(ep => ep.isCompleted).length}
                completedQuizzes={lesson.quizzes.filter(quiz => quiz.isCompleted).length}
              />
            </div>

            {activeTab === 'episodes' && (
              <div className="space-y-8">
                {/* Video Player */}
                {currentEpisode && (
                  <div className="bg-white rounded-xl shadow-sm p-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">
                      {currentEpisode.title}
                    </h2>
                    <VideoPlayer
                      episode={currentEpisode}
                      onProgress={handleVideoProgress}
                      onComplete={handleVideoComplete}
                    />
                    <p className="text-gray-600 mt-4">
                      {currentEpisode.description}
                    </p>
                  </div>
                )}

                {/* Episode Carousel */}
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">
                    All Episodes
                  </h3>
                  <EpisodeCarousel
                    episodes={lesson.episodes}
                    currentEpisodeId={currentEpisode?.id}
                    onEpisodeClick={handleEpisodeClick}
                  />
                </div>
              </div>
            )}

            {activeTab === 'quizzes' && !showQuizEngine && !quizResults && (
              <div className="space-y-6">
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-6">
                    Quizzes
                  </h2>
                  <p className="text-gray-600 mb-6">
                    Test your knowledge after watching each episode. Complete episodes to unlock their quizzes.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {lesson.quizzes.map((quiz) => (
                      <QuizCard
                        key={quiz.id}
                        quiz={quiz}
                        onClick={handleQuizClick}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'quizzes' && showQuizEngine && activeQuiz && (
              <QuizEngine
                quiz={activeQuiz}
                onComplete={handleQuizComplete}
                onExit={handleQuizExit}
              />
            )}

            {activeTab === 'quizzes' && quizResults && (
              <QuizResults
                results={quizResults}
                onRetake={handleQuizRetake}
                onContinue={handleQuizResultsContinue}
                canRetake={true}
              />
            )}

            {activeTab === 'memory-card' && lesson.memorizationCard && (
              <MemorizationCard
                card={lesson.memorizationCard}
                onComplete={handleMemoryCardComplete}
              />
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};
