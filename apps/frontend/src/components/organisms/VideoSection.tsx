import React from 'react';
import { VideoPlayer } from '../molecules/VideoPlayer';
import { Button } from '../atoms/Button';

export const VideoSection: React.FC = () => {
  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
            See it in Action
          </h2>
          <div className="max-w-3xl">
            <p className="text-gray-600 text-lg leading-relaxed mb-6">
              Watch a sample video to get a feel for how we turn complex anatomy into engaging, memorable stories.
            </p>
            <p className="text-gray-600 text-base">
              Want more? Sign up for free to unlock access to more content!
            </p>
          </div>
          <div className="mt-8">
            <Button variant="secondary" size="lg" className="shadow-md hover:shadow-lg">
              SIGN UP
            </Button>
          </div>
        </div>

        <div className="max-w-4xl mx-auto">
          <VideoPlayer />
        </div>
      </div>
    </section>
  );
};