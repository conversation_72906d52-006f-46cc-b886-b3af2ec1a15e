import React from 'react';
import { Button } from '../atoms/Button';

export const HeroSection: React.FC = () => {
  return (
    <section className="bg-gradient-to-br from-gray-50 to-blue-50 py-12 lg:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
            Welcome to{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600">
              Anatomy Land
            </span>
          </h1>
          
          <p className="text-lg sm:text-xl text-gray-600 mb-4 font-medium">
            For MD, PhD, PA and PT
          </p>
          
          <h2 className="text-xl sm:text-2xl font-semibold text-gray-800 mb-6">
            Built to accelerate your memorization of anatomy coursework
          </h2>
          
          <p className="text-gray-600 text-base sm:text-lg leading-relaxed max-w-3xl mb-8">
            Anatomy Land is a visual memorization tool designed to support learning in the cadaver lab. 
            Explore a map that mirrors human anatomical regions, each filled with themed videos featuring 
            characters, colors, and objects that bring each lesson to life. Reinforce your learning with 
            memorization cards, test your recall with expert-crafted quizzes, and track your progress as 
            you earn badges for mastering each region.
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button size="lg" className="shadow-md hover:shadow-lg">
              Start Your Journey
            </Button>
            <Button variant="outline" size="lg">
              Watch Demo
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};