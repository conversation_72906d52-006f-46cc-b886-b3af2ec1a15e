import React from 'react';
import { Facebook, Linkedin, Instagram, Youtube } from 'lucide-react';
import { FooterSection, SocialLink } from '../../types';

const footerSections: FooterSection[] = [
  {
    title: 'Company',
    links: [
      { label: 'About', href: '#about' },
      { label: 'Packages', href: '#packages' },
      { label: 'FAQs', href: '#faqs' }
    ]
  },
  {
    title: 'Account',
    links: [
      { label: 'My Account', href: '#account' },
      { label: 'Subscription', href: '#subscription' }
    ]
  },
  {
    title: 'Resources',
    links: [
      { label: 'Privacy Policy', href: '#privacy' },
      { label: 'Terms and Conditions', href: '#terms' }
    ]
  }
];

const socialLinks: SocialLink[] = [
  { name: 'Facebook', href: '#facebook', icon: 'facebook' },
  { name: 'LinkedIn', href: '#linkedin', icon: 'linkedin' },
  { name: 'Instagram', href: '#instagram', icon: 'instagram' },
  { name: 'YouTube', href: '#youtube', icon: 'youtube' }
];

export const Footer: React.FC = () => {
  const getSocialIcon = (iconName: string) => {
    const iconProps = { className: "h-5 w-5" };
    switch (iconName) {
      case 'facebook': return <Facebook {...iconProps} />;
      case 'linkedin': return <Linkedin {...iconProps} />;
      case 'instagram': return <Instagram {...iconProps} />;
      case 'youtube': return <Youtube {...iconProps} />;
      default: return null;
    }
  };

  return (
    <footer className="bg-gray-50 border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-1">
            <h3 className="font-semibold text-gray-900 mb-4">Support</h3>
            <p className="text-gray-600 mb-4">
              <a href="mailto:<EMAIL>" className="hover:text-blue-600 transition-colors duration-200">
                <EMAIL>
              </a>
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  className="text-gray-400 hover:text-blue-600 transition-colors duration-200"
                  aria-label={social.name}
                >
                  {getSocialIcon(social.icon)}
                </a>
              ))}
            </div>
          </div>

          {/* Footer Sections */}
          {footerSections.map((section) => (
            <div key={section.title} className="md:col-span-1">
              <h3 className="font-semibold text-gray-900 mb-4">{section.title}</h3>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.label}>
                    <a
                      href={link.href}
                      className="text-gray-600 hover:text-blue-600 transition-colors duration-200"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="border-t border-gray-200 mt-12 pt-8">
          <p className="text-gray-500 text-sm">
            ©2025 Milo's Academy, Inc. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};