import React, { useState, useRef, useEffect } from 'react';
import { X, Maximize, Minimize, ExternalLink, Clock } from 'lucide-react';
import { cn } from '../../utils/cn';
import { MemorizationCard as MemorizationCardType } from '../../types';

interface MemorizationCardProps {
  card: MemorizationCardType;
  onComplete?: (cardId: string, timeSpent: number) => void;
  onClose?: () => void;
  className?: string;
}

export const MemorizationCard: React.FC<MemorizationCardProps> = ({
  card,
  onComplete,
  onClose,
  className
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [timeSpent, setTimeSpent] = useState(0);
  const [isCompleted, setIsCompleted] = useState(card.completedAt ? true : false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const startTimeRef = useRef<number>(Date.now());
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Track time spent
  useEffect(() => {
    startTimeRef.current = Date.now();
    
    intervalRef.current = setInterval(() => {
      setTimeSpent(Math.floor((Date.now() - startTimeRef.current) / 1000));
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleComplete = () => {
    if (!isCompleted && onComplete) {
      setIsCompleted(true);
      onComplete(card.id, timeSpent);
    }
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  const openInNewTab = () => {
    window.open(card.thingLinkUrl, '_blank');
  };

  if (!card.isUnlocked) {
    return (
      <div className={cn("bg-white rounded-xl shadow-sm p-8 text-center", className)}>
        <div className="max-w-md mx-auto">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Memory Card Locked
          </h3>
          <p className="text-gray-600 mb-4">
            Complete all episodes and quizzes in this lesson to unlock the memory card.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-blue-800 text-sm">
              💡 Memory cards provide interactive visual summaries to help reinforce your learning!
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "bg-white rounded-xl shadow-sm overflow-hidden",
      isFullscreen && "fixed inset-0 z-50 rounded-none",
      className
    )}>
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h2 className="text-xl font-semibold mb-1">{card.title}</h2>
            <p className="text-purple-100 text-sm">{card.description}</p>
          </div>
          
          <div className="flex items-center space-x-2 ml-4">
            {/* Time Tracker */}
            <div className="flex items-center space-x-1 bg-white/20 rounded-full px-3 py-1">
              <Clock className="h-4 w-4" />
              <span className="text-sm font-medium">{formatTime(timeSpent)}</span>
            </div>
            
            {/* Controls */}
            <button
              onClick={openInNewTab}
              className="p-2 hover:bg-white/20 rounded-full transition-colors duration-200"
              title="Open in new tab"
            >
              <ExternalLink className="h-5 w-5" />
            </button>
            
            <button
              onClick={handleFullscreen}
              className="p-2 hover:bg-white/20 rounded-full transition-colors duration-200"
              title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? (
                <Minimize className="h-5 w-5" />
              ) : (
                <Maximize className="h-5 w-5" />
              )}
            </button>
            
            {onClose && (
              <button
                onClick={handleClose}
                className="p-2 hover:bg-white/20 rounded-full transition-colors duration-200"
                title="Close"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* ThingLink Iframe */}
      <div className={cn(
        "relative bg-gray-100",
        isFullscreen ? "h-[calc(100vh-80px)]" : "h-96 md:h-[500px]"
      )}>
        <iframe
          ref={iframeRef}
          src={card.thingLinkUrl}
          className="w-full h-full border-0"
          allowFullScreen
          title={card.title}
          onLoad={() => {
            // Track that the card has been loaded/viewed
            if (!isCompleted) {
              setTimeout(() => {
                handleComplete();
              }, 30000); // Auto-complete after 30 seconds of viewing
            }
          }}
        />
        
        {/* Loading Overlay */}
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-2"></div>
            <p className="text-gray-600 text-sm">Loading interactive memory card...</p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 bg-gray-50 border-t">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {isCompleted ? (
              <div className="flex items-center space-x-2 text-green-600">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">Completed</span>
              </div>
            ) : (
              <div className="text-gray-600">
                <span className="text-sm">Explore the interactive hotspots to complete</span>
              </div>
            )}
          </div>
          
          <div className="flex space-x-3">
            {!isCompleted && (
              <button
                onClick={handleComplete}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors duration-200"
              >
                Mark Complete
              </button>
            )}
            
            <button
              onClick={openInNewTab}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200"
            >
              Open Full View
            </button>
          </div>
        </div>
      </div>

      {/* Instructions */}
      {!isCompleted && (
        <div className="p-4 bg-blue-50 border-t border-blue-200">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h4 className="font-medium text-blue-900 mb-1">How to use this memory card:</h4>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• Click on the interactive hotspots to reveal key information</li>
                <li>• Explore all areas to reinforce your learning</li>
                <li>• Use this as a quick reference for review</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
