import React from 'react';
import { FeatureCard } from '../molecules/FeatureCard';
import { Carousel } from '../molecules/Carousel';
import { FeatureCard as FeatureCardType } from '../../types';

const features: FeatureCardType[] = [
  {
    id: 'storytelling',
    title: 'Memorable storytelling',
    description: 'Anatomy Land uses engaging storytelling to help you memorize everything from innervation to muscle groupings.',
    image: 'https://images.pexels.com/photos/159832/book-open-book-read-literature-159832.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    imageAlt: 'Open book representing storytelling approach to anatomy learning'
  },
  {
    id: 'exam-prep',
    title: 'Exam preparation',
    description: 'Our curriculum is organized by regions and systems to help you study for your anatomy coursework and exams with ease.',
    image: 'https://images.pexels.com/photos/5212317/pexels-photo-5212317.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    imageAlt: 'Medical students studying anatomy with textbooks and models'
  },
  {
    id: 'multisensory',
    title: 'Multisensory memorization',
    description: 'Our video lessons assist with multisensory memorization. Our memory devices are both visual-based and language-based.',
    image: 'https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    imageAlt: 'Interactive anatomy learning with visual and audio elements'
  },
  {
    id: 'quizzes',
    title: 'Quizzes after each video',
    description: 'Test your memory after watching a video. Use the quizzes to help review the content and reinforce short-term and long-term memory.',
    image: 'https://images.pexels.com/photos/5212345/pexels-photo-5212345.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    imageAlt: 'Student taking an anatomy quiz on a tablet device'
  }
];

export const FeaturesSection: React.FC = () => {
  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Desktop Grid Layout */}
        <div className="hidden lg:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          {features.map((feature) => (
            <FeatureCard key={feature.id} feature={feature} />
          ))}
        </div>

        {/* Mobile Carousel Layout */}
        <div className="lg:hidden">
          <Carousel showDots={true} autoPlay={false}>
            {features.map((feature) => (
              <FeatureCard key={feature.id} feature={feature} />
            ))}
          </Carousel>
        </div>
      </div>
    </section>
  );
};