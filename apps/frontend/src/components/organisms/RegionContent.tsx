import React, { useState, useEffect } from 'react';
import { Header } from './Header';
import { Footer } from './Footer';
import { LessonCard } from '../molecules/LessonCard';
import { Breadcrumb } from '../molecules/Breadcrumb';
import { Button } from '../atoms/Button';

interface Lesson {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  episodeCount: number;
  duration: string;
  completionRate: number;
  isCompleted?: boolean;
}

interface RegionContentProps {
  regionId: string;
  regionName: string;
  regionDescription: string;
  isLoggedIn?: boolean;
  userEmail?: string;
}

// Mock data - replace with API call
const mockLessons: Lesson[] = [
  {
    id: 'lesson-1',
    title: 'Lesson 1: King Idols Tomb',
    description: 'Master 12 cranial nerves through 6 focused episodes',
    thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    episodeCount: 6,
    duration: '20:15',
    completionRate: 60,
  },
  {
    id: 'lesson-2',
    title: 'Lesson 2: All Hail the Superb Pharoah of the Cranial Dynasty',
    description: 'Comprehensive overview of facial muscles and expressions',
    thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    episodeCount: 6,
    duration: '20:15',
    completionRate: 0,
  },
  {
    id: 'lesson-3',
    title: 'Lesson 3: The Inferior Heir to the Throne Plans a Coup',
    description: 'Understanding neck triangles through mini lessons',
    thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    episodeCount: 5,
    duration: '20:15',
    completionRate: 0,
  },
  {
    id: 'lesson-4',
    title: 'Lesson 4: A secret Bow Tie Roid Closet',
    description: 'Comprehensive overview of facial muscles and expressions',
    thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    episodeCount: 6,
    duration: '20:15',
    completionRate: 0,
  },
  {
    id: 'lesson-5',
    title: 'Lesson 5: A Pair of Rice Piles',
    description: 'Understanding neck triangles through mini lessons',
    thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    episodeCount: 5,
    duration: '20:15',
    completionRate: 0,
  },
  {
    id: 'lesson-6',
    title: 'Lesson: Take a Front Seat at the Frontal Lobe',
    description: 'Understanding neck triangles through mini lessons',
    thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    episodeCount: 6,
    duration: '20:15',
    completionRate: 0,
  },
  // Additional lessons for "View More" functionality
  {
    id: 'lesson-7',
    title: 'Lesson 7: Advanced Cranial Structures',
    description: 'Deep dive into complex cranial anatomy',
    thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    episodeCount: 8,
    duration: '25:30',
    completionRate: 0,
  },
  {
    id: 'lesson-8',
    title: 'Lesson 8: Neurological Pathways',
    description: 'Understanding neural connections and pathways',
    thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    episodeCount: 7,
    duration: '22:45',
    completionRate: 0,
  }
];

export const RegionContent: React.FC<RegionContentProps> = ({
  regionId,
  regionName,
  regionDescription,
  isLoggedIn = false,
  userEmail = "<EMAIL>"
}) => {
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [displayedLessons, setDisplayedLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [showingAll, setShowingAll] = useState(false);

  // Initial cards to show
  const getInitialCardCount = () => {
    if (typeof window !== 'undefined') {
      return window.innerWidth >= 768 ? 6 : 4; // Desktop: 6, Mobile: 4
    }
    return 6;
  };

  const [initialCardCount] = useState(getInitialCardCount());

  useEffect(() => {
    // Simulate API call
    const fetchLessons = async () => {
      setLoading(true);
      try {
        // In real implementation, replace with actual API call
        // const response = await fetch(`/api/anatomy/regions/${regionId}/lessons`);
        // const data = await response.json();
        
        // Using mock data for now
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate loading
        setLessons(mockLessons);
        setDisplayedLessons(mockLessons.slice(0, initialCardCount));
      } catch (error) {
        console.error('Failed to fetch lessons:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLessons();
  }, [regionId, initialCardCount]);

  const handleViewMore = () => {
    setDisplayedLessons(lessons);
    setShowingAll(true);
  };

  const handleLessonClick = (lessonId: string) => {
    console.log(`Navigate to lesson: ${lessonId}`);
    // In real implementation, navigate to lesson page
  };

  const breadcrumbItems = [
    { label: 'Map', href: '/map' },
    { label: regionName, isActive: true }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex flex-col">
        <Header isLoggedIn={isLoggedIn} userEmail={userEmail} />
        <main className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Header isLoggedIn={isLoggedIn} userEmail={userEmail} />
      
      <main className="flex-1">
        {/* Page Header */}
        <section className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
            {/* Breadcrumb */}
            <Breadcrumb items={breadcrumbItems} className="mb-4" />
            
            {/* Region Title and Description */}
            <div className="max-w-4xl">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3">
                {regionName}
              </h1>
              <p className="text-gray-600 text-base sm:text-lg">
                {regionDescription}
              </p>
            </div>
          </div>
        </section>

        {/* Lessons Grid */}
        <section className="py-8 sm:py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Grid Layout */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
              {displayedLessons.map((lesson) => (
                <LessonCard
                  key={lesson.id}
                  lesson={lesson}
                  onClick={() => handleLessonClick(lesson.id)}
                  className="transform hover:-translate-y-1"
                />
              ))}
            </div>

            {/* View More Button */}
            {!showingAll && lessons.length > displayedLessons.length && (
              <div className="mt-12 text-center">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleViewMore}
                  className="px-8 py-3 text-base font-medium min-w-[200px]"
                >
                  View more
                </Button>
              </div>
            )}

            {/* Empty State */}
            {lessons.length === 0 && !loading && (
              <div className="text-center py-12">
                <div className="max-w-md mx-auto">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No lessons available
                  </h3>
                  <p className="text-gray-600">
                    Lessons for this region are coming soon. Check back later!
                  </p>
                </div>
              </div>
            )}
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};