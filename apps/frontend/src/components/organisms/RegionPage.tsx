import React from 'react';
import { RegionContent } from './RegionContent';

interface RegionPageProps {
  regionId?: string;
}

export const RegionPage: React.FC<RegionPageProps> = ({ regionId = 'cranial-cavity' }) => {
  // This would typically come from routing params and API
  const regionData = {
    'cranial-cavity': {
      name: 'Cranial Cavity region',
      description: 'Explore 6 lessons covering head & neck anatomy'
    },
    'thorax': {
      name: 'Thorax region',
      description: 'Explore 8 lessons covering chest anatomy'
    },
    'abdomen': {
      name: 'Abdomen region', 
      description: 'Explore 10 lessons covering abdominal anatomy'
    }
    // Add more regions as needed
  };

  const region = regionData[regionId as keyof typeof regionData] || regionData['cranial-cavity'];
  
  // This should come from your auth context
  const isLoggedIn = true;
  const userEmail = "<EMAIL>";

  return (
    <RegionContent
      regionId={regionId}
      regionName={region.name}
      regionDescription={region.description}
      isLoggedIn={isLoggedIn}
      userEmail={userEmail}
    />
  );
};