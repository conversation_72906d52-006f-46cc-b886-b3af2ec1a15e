import React from 'react';
import { Check<PERSON>ircle, XCircle, RotateCcw, ArrowRight } from 'lucide-react';
import { cn } from '../../utils/cn';

interface QuizResultsProps {
  results: {
    quizId: string;
    score: number;
    totalQuestions: number;
    percentage: number;
    passed: boolean;
  };
  onRetake?: () => void;
  onContinue: () => void;
  canRetake?: boolean;
  className?: string;
}

export const QuizResults: React.FC<QuizResultsProps> = ({
  results,
  onRetake,
  onContinue,
  canRetake = true,
  className
}) => {
  const getGradeColor = () => {
    if (results.percentage >= 90) return 'text-green-600';
    if (results.percentage >= 80) return 'text-blue-600';
    if (results.percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getGradeBgColor = () => {
    if (results.percentage >= 90) return 'bg-green-50 border-green-200';
    if (results.percentage >= 80) return 'bg-blue-50 border-blue-200';
    if (results.percentage >= 70) return 'bg-yellow-50 border-yellow-200';
    return 'bg-red-50 border-red-200';
  };

  const getGradeText = () => {
    if (results.percentage >= 90) return 'Excellent!';
    if (results.percentage >= 80) return 'Great job!';
    if (results.percentage >= 70) return 'Good work!';
    return 'Keep practicing!';
  };

  return (
    <div className={cn("bg-white rounded-xl shadow-lg overflow-hidden", className)}>
      {/* Header */}
      <div className={cn(
        "p-6 border-b",
        results.passed ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
      )}>
        <div className="flex items-center justify-center mb-4">
          {results.passed ? (
            <CheckCircle className="h-16 w-16 text-green-600" />
          ) : (
            <XCircle className="h-16 w-16 text-red-600" />
          )}
        </div>
        
        <h2 className="text-2xl font-bold text-center text-gray-900 mb-2">
          Quiz Complete!
        </h2>
        
        <p className={cn(
          "text-center text-lg font-medium",
          results.passed ? "text-green-700" : "text-red-700"
        )}>
          {results.passed ? 'Congratulations! You passed.' : 'You need 70% to pass.'}
        </p>
      </div>

      {/* Results */}
      <div className="p-6">
        {/* Score Display */}
        <div className={cn(
          "text-center p-6 rounded-lg border-2 mb-6",
          getGradeBgColor()
        )}>
          <div className={cn("text-4xl font-bold mb-2", getGradeColor())}>
            {results.percentage}%
          </div>
          <div className="text-lg text-gray-700 mb-1">
            {results.score} out of {results.totalQuestions} correct
          </div>
          <div className={cn("font-medium", getGradeColor())}>
            {getGradeText()}
          </div>
        </div>

        {/* Performance Breakdown */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {results.score}
            </div>
            <div className="text-sm text-gray-600">Correct</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">
              {results.totalQuestions - results.score}
            </div>
            <div className="text-sm text-gray-600">Incorrect</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Your Score</span>
            <span>Passing Score (70%)</span>
          </div>
          <div className="relative w-full bg-gray-200 rounded-full h-3">
            <div 
              className={cn(
                "h-3 rounded-full transition-all duration-500",
                results.passed ? "bg-green-500" : "bg-red-500"
              )}
              style={{ width: `${Math.min(results.percentage, 100)}%` }}
            />
            {/* Passing threshold line */}
            <div 
              className="absolute top-0 w-0.5 h-3 bg-gray-400"
              style={{ left: '70%' }}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          {canRetake && onRetake && (
            <button
              onClick={onRetake}
              className="flex items-center justify-center space-x-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200"
            >
              <RotateCcw className="h-4 w-4" />
              <span>Retake Quiz</span>
            </button>
          )}
          
          <button
            onClick={onContinue}
            className="flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 flex-1"
          >
            <span>Continue Learning</span>
            <ArrowRight className="h-4 w-4" />
          </button>
        </div>

        {/* Encouragement Message */}
        {!results.passed && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-blue-800 text-sm text-center">
              Don't worry! Review the episode content and try again. You can do this! 💪
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
