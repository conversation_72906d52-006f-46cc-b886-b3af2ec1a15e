import React, { useState, useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '../../utils/cn';
import { Episode } from '../../types';
import { EpisodeCard } from './EpisodeCard';

interface EpisodeCarouselProps {
  episodes: Episode[];
  currentEpisodeId?: string;
  onEpisodeClick: (episode: Episode) => void;
  className?: string;
}

export const EpisodeCarousel: React.FC<EpisodeCarouselProps> = ({
  episodes,
  currentEpisodeId,
  onEpisodeClick,
  className
}) => {
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollButtons();
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollButtons);
      return () => container.removeEventListener('scroll', checkScrollButtons);
    }
  }, [episodes]);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const scrollAmount = 320; // Width of one card plus gap
      const newScrollLeft = scrollContainerRef.current.scrollLeft + 
        (direction === 'left' ? -scrollAmount : scrollAmount);
      
      scrollContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  if (episodes.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No episodes available</p>
      </div>
    );
  }

  return (
    <div className={cn("relative", className)}>
      {/* Navigation Buttons */}
      <button
        onClick={() => scroll('left')}
        disabled={!canScrollLeft}
        className={cn(
          "absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white shadow-lg rounded-full p-2 transition-all duration-200",
          "focus:outline-none focus:ring-2 focus:ring-blue-500",
          canScrollLeft
            ? "hover:bg-gray-50 text-gray-700"
            : "opacity-50 cursor-not-allowed text-gray-400"
        )}
        style={{ transform: 'translateY(-50%) translateX(-50%)' }}
      >
        <ChevronLeft className="h-5 w-5" />
      </button>

      <button
        onClick={() => scroll('right')}
        disabled={!canScrollRight}
        className={cn(
          "absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white shadow-lg rounded-full p-2 transition-all duration-200",
          "focus:outline-none focus:ring-2 focus:ring-blue-500",
          canScrollRight
            ? "hover:bg-gray-50 text-gray-700"
            : "opacity-50 cursor-not-allowed text-gray-400"
        )}
        style={{ transform: 'translateY(-50%) translateX(50%)' }}
      >
        <ChevronRight className="h-5 w-5" />
      </button>

      {/* Carousel Container */}
      <div
        ref={scrollContainerRef}
        className="flex space-x-4 overflow-x-auto scrollbar-hide pb-4 px-8"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {episodes.map((episode) => (
          <div key={episode.id} className="flex-none w-80">
            <EpisodeCard
              episode={episode}
              isCurrentlyPlaying={episode.id === currentEpisodeId}
              onClick={onEpisodeClick}
            />
          </div>
        ))}
      </div>

      {/* Mobile Touch Indicator */}
      <div className="md:hidden text-center mt-2">
        <p className="text-xs text-gray-500">Swipe to browse episodes</p>
      </div>
    </div>
  );
};
