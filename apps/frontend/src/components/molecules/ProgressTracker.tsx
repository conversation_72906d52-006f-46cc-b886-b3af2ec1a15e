import React from 'react';
import { CheckCircle, Circle, Lock, Trophy, Star } from 'lucide-react';
import { cn } from '../../utils/cn';
import { LessonStatus } from '../../types';

interface ProgressTrackerProps {
  status: LessonStatus;
  episodeCount: number;
  quizCount: number;
  completedEpisodes: number;
  completedQuizzes: number;
  className?: string;
}

export const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  status,
  episodeCount,
  quizCount,
  completedEpisodes,
  completedQuizzes,
  className
}) => {
  const progressSteps = [
    {
      id: 'episodes',
      label: 'Watch Episodes',
      description: `${completedEpisodes}/${episodeCount} episodes completed`,
      isCompleted: status.allEpisodesCompleted,
      isActive: !status.allEpisodesCompleted,
      icon: Circle
    },
    {
      id: 'quizzes',
      label: 'Complete Quizzes',
      description: `${completedQuizzes}/${quizCount} quizzes completed`,
      isCompleted: status.allQuizzesCompleted,
      isActive: status.allEpisodesCompleted && !status.allQuizzesCompleted,
      isLocked: !status.allEpisodesCompleted,
      icon: Circle
    },
    {
      id: 'memory-card',
      label: 'Study Memory Card',
      description: status.memorizationCardCompleted ? 'Memory card completed' : 'Interactive visual summary',
      isCompleted: status.memorizationCardCompleted,
      isActive: status.memorizationCardUnlocked && !status.memorizationCardCompleted,
      isLocked: !status.memorizationCardUnlocked,
      icon: Circle
    },
    {
      id: 'mastery',
      label: 'Lesson Mastery',
      description: status.masteryAchieved ? 'Congratulations! Lesson mastered' : 'Complete all steps to achieve mastery',
      isCompleted: status.masteryAchieved,
      isActive: false,
      icon: Trophy
    }
  ];

  const getStepIcon = (step: typeof progressSteps[0]) => {
    const IconComponent = step.icon;
    
    if (step.isCompleted) {
      return <CheckCircle className="h-6 w-6 text-green-600" />;
    }
    
    if (step.isLocked) {
      return <Lock className="h-6 w-6 text-gray-400" />;
    }
    
    if (step.id === 'mastery') {
      return <Trophy className="h-6 w-6 text-yellow-500" />;
    }
    
    return <IconComponent className={cn(
      "h-6 w-6",
      step.isActive ? "text-blue-600" : "text-gray-400"
    )} />;
  };

  const getStepColor = (step: typeof progressSteps[0]) => {
    if (step.isCompleted) {
      return 'text-green-600';
    }
    
    if (step.isLocked) {
      return 'text-gray-400';
    }
    
    if (step.isActive) {
      return 'text-blue-600';
    }
    
    return 'text-gray-600';
  };

  const getStepBgColor = (step: typeof progressSteps[0]) => {
    if (step.isCompleted) {
      return 'bg-green-50 border-green-200';
    }
    
    if (step.isActive) {
      return 'bg-blue-50 border-blue-200';
    }
    
    return 'bg-gray-50 border-gray-200';
  };

  const overallProgress = Math.round(
    ((completedEpisodes / episodeCount) * 25) +
    ((completedQuizzes / quizCount) * 25) +
    (status.memorizationCardCompleted ? 25 : 0) +
    (status.masteryAchieved ? 25 : 0)
  );

  return (
    <div className={cn("bg-white rounded-xl shadow-sm p-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Learning Progress
        </h3>
        
        {status.masteryAchieved && (
          <div className="flex items-center space-x-2 bg-yellow-50 border border-yellow-200 rounded-full px-3 py-1">
            <Star className="h-4 w-4 text-yellow-600" />
            <span className="text-yellow-800 text-sm font-medium">Mastered!</span>
          </div>
        )}
      </div>

      {/* Overall Progress Bar */}
      <div className="mb-6">
        <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
          <span>Overall Progress</span>
          <span>{overallProgress}% complete</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={cn(
              "h-3 rounded-full transition-all duration-500",
              status.masteryAchieved ? "bg-yellow-500" : "bg-blue-500"
            )}
            style={{ width: `${overallProgress}%` }}
          />
        </div>
      </div>

      {/* Progress Steps */}
      <div className="space-y-4">
        {progressSteps.map((step, index) => (
          <div key={step.id} className="relative">
            {/* Connector Line */}
            {index < progressSteps.length - 1 && (
              <div className="absolute left-3 top-8 w-0.5 h-8 bg-gray-200" />
            )}
            
            <div className={cn(
              "flex items-start space-x-4 p-4 rounded-lg border-2 transition-all duration-200",
              getStepBgColor(step)
            )}>
              {/* Icon */}
              <div className="flex-shrink-0 mt-0.5">
                {getStepIcon(step)}
              </div>
              
              {/* Content */}
              <div className="flex-1 min-w-0">
                <h4 className={cn(
                  "font-medium mb-1",
                  getStepColor(step)
                )}>
                  {step.label}
                </h4>
                <p className={cn(
                  "text-sm",
                  step.isLocked ? "text-gray-400" : "text-gray-600"
                )}>
                  {step.description}
                </p>
                
                {/* Step-specific progress */}
                {step.id === 'episodes' && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div 
                        className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${(completedEpisodes / episodeCount) * 100}%` }}
                      />
                    </div>
                  </div>
                )}
                
                {step.id === 'quizzes' && !step.isLocked && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div 
                        className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${(completedQuizzes / quizCount) * 100}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>
              
              {/* Status Badge */}
              {step.isCompleted && (
                <div className="flex-shrink-0">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Complete
                  </span>
                </div>
              )}
              
              {step.isActive && (
                <div className="flex-shrink-0">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    In Progress
                  </span>
                </div>
              )}
              
              {step.isLocked && (
                <div className="flex-shrink-0">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                    Locked
                  </span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Mastery Achievement */}
      {status.masteryAchieved && (
        <div className="mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center">
                <Trophy className="h-6 w-6 text-white" />
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-yellow-900 mb-1">
                🎉 Lesson Mastered!
              </h4>
              <p className="text-yellow-800 text-sm">
                You've successfully completed all episodes, quizzes, and the memory card. Great job!
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
