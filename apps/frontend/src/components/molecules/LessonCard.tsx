import React from 'react';
import { Play, Clock, Users } from 'lucide-react';
import { cn } from '../../utils/cn';

interface LessonCardProps {
  lesson: {
    id: string;
    title: string;
    description: string;
    thumbnail: string;
    episodeCount: number;
    duration: string;
    completionRate: number;
    isCompleted?: boolean;
  };
  onClick?: () => void;
  className?: string;
}

export const LessonCard: React.FC<LessonCardProps> = ({
  lesson,
  onClick,
  className
}) => {
  const formatDuration = (duration: string) => {
    // Convert duration like "20:15" to readable format
    const [minutes, seconds] = duration.split(':');
    return `${minutes}:${seconds}`;
  };

  return (
    <div
      className={cn(
        "bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden cursor-pointer group border border-gray-100",
        className
      )}
      onClick={onClick}
    >
      {/* Thumbnail */}
      <div className="aspect-video relative overflow-hidden bg-gray-100">
        <img
          src={lesson.thumbnail}
          alt={lesson.title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          loading="lazy"
        />
        
        {/* Play Overlay */}
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <div className="bg-white/90 rounded-full p-3 transform scale-90 group-hover:scale-100 transition-transform duration-200">
            <Play className="h-6 w-6 text-gray-900 ml-0.5" />
          </div>
        </div>

        {/* Completion Badge */}
        {lesson.completionRate > 0 && (
          <div className="absolute top-3 right-3">
            <div className={cn(
              "px-2 py-1 rounded-full text-xs font-medium",
              lesson.completionRate === 100 
                ? "bg-green-100 text-green-800" 
                : "bg-blue-100 text-blue-800"
            )}>
              {lesson.completionRate}% complete
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-4 sm:p-5">
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
          {lesson.title}
        </h3>
        
        <p className="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
          {lesson.description}
        </p>

        {/* Metadata */}
        <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-1">
              <Users className="h-3 w-3" />
              <span>{lesson.episodeCount} episodes</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{formatDuration(lesson.duration)}</span>
            </div>
          </div>
        </div>

        {/* Watch Button */}
        <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2.5 px-4 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2">
          <span>Watch now</span>
          <Play className="h-4 w-4 ml-1" />
        </button>
      </div>
    </div>
  );
};