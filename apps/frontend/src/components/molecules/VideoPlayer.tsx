import React, { useState } from 'react';
import { Play, Pause, Volume2, Maximize, Settings } from 'lucide-react';

export const VideoPlayer: React.FC = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const totalTime = 459; // 7:39 in seconds

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = (currentTime / totalTime) * 100;

  return (
    <div className="relative bg-black rounded-xl overflow-hidden shadow-2xl group">
      {/* Video Thumbnail */}
      <div className="aspect-video relative">
        <img
          src="https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=1200&h=675&fit=crop"
          alt="Anatomy education video preview"
          className="w-full h-full object-cover"
        />
        
        {/* Play Overlay */}
        <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
          <button
            onClick={() => setIsPlaying(!isPlaying)}
            className="bg-white/90 hover:bg-white rounded-full p-4 transition-all duration-200 transform hover:scale-110"
          >
            {isPlaying ? (
              <Pause className="h-8 w-8 text-gray-900" />
            ) : (
              <Play className="h-8 w-8 text-gray-900 ml-1" />
            )}
          </button>
        </div>

        {/* Film Strip Effect */}
        <div className="absolute top-4 right-4">
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-2 transform rotate-12 shadow-lg">
            <div className="w-16 h-12 bg-white/20 rounded border-2 border-white/30"></div>
          </div>
        </div>
      </div>

      {/* Video Controls */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
        {/* Progress Bar */}
        <div className="mb-3">
          <div className="w-full bg-white/20 rounded-full h-1 cursor-pointer">
            <div
              className="bg-blue-500 h-1 rounded-full transition-all duration-200"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Control Buttons */}
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setIsPlaying(!isPlaying)}
              className="hover:bg-white/20 rounded p-1 transition-colors duration-200"
            >
              {isPlaying ? (
                <Pause className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5" />
              )}
            </button>
            <span className="text-sm font-mono">
              {formatTime(currentTime)} / {formatTime(totalTime)}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <button className="hover:bg-white/20 rounded p-1 transition-colors duration-200">
              <Volume2 className="h-5 w-5" />
            </button>
            <button className="hover:bg-white/20 rounded p-1 transition-colors duration-200">
              <Settings className="h-5 w-5" />
            </button>
            <button className="hover:bg-white/20 rounded p-1 transition-colors duration-200">
              <Maximize className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};