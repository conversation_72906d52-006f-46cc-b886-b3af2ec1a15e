import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, Maximize, Settings } from 'lucide-react';
import { Episode } from '../../types';

interface VideoPlayerProps {
  episode?: Episode;
  onProgress?: (progress: {
    watchedDuration: number;
    totalDuration: number;
    completionPercentage: number;
    isCompleted: boolean;
  }) => void;
  onComplete?: (episodeId: string) => void;
  className?: string;
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  episode,
  onProgress,
  onComplete,
  className
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [hasCompletedOnce, setHasCompletedOnce] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const totalTime = episode?.duration || 459; // Default to 7:39 in seconds

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = (currentTime / totalTime) * 100;

  // Update progress and check for completion
  const updateProgress = (watchedDuration: number) => {
    const completionPercentage = Math.min((watchedDuration / totalTime) * 100, 100);
    const isCompleted = completionPercentage >= 100;

    // Call progress callback
    if (onProgress) {
      onProgress({
        watchedDuration,
        totalDuration: totalTime,
        completionPercentage,
        isCompleted
      });
    }

    // Call completion callback only once per episode
    if (isCompleted && !hasCompletedOnce && episode && onComplete) {
      setHasCompletedOnce(true);
      onComplete(episode.id);
    }
  };

  // Start progress tracking when playing
  const startProgressTracking = () => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    progressIntervalRef.current = setInterval(() => {
      if (videoRef.current && !videoRef.current.paused) {
        const currentTime = videoRef.current.currentTime;
        setCurrentTime(currentTime);
        updateProgress(currentTime);
      }
    }, 1000); // Update every second
  };

  // Stop progress tracking
  const stopProgressTracking = () => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  };

  // Handle play/pause
  const togglePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
        stopProgressTracking();
      } else {
        videoRef.current.play();
        startProgressTracking();
      }
      setIsPlaying(!isPlaying);
    }
  };

  // Handle video events
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
      updateProgress(video.currentTime);
    };

    const handlePlay = () => {
      setIsPlaying(true);
      startProgressTracking();
    };

    const handlePause = () => {
      setIsPlaying(false);
      stopProgressTracking();
    };

    const handleEnded = () => {
      setIsPlaying(false);
      stopProgressTracking();
      // Ensure completion is tracked when video ends
      updateProgress(totalTime);
    };

    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('ended', handleEnded);

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('ended', handleEnded);
      stopProgressTracking();
    };
  }, [episode, totalTime, hasCompletedOnce]);

  // Reset completion tracking when episode changes
  useEffect(() => {
    setHasCompletedOnce(false);
    setCurrentTime(0);
    if (episode?.isCompleted) {
      setHasCompletedOnce(true);
    }
  }, [episode?.id]);

  return (
    <div className={`relative bg-black rounded-xl overflow-hidden shadow-2xl group ${className || ''}`}>
      {/* Video Element (hidden, for tracking) */}
      <video
        ref={videoRef}
        className="hidden"
        preload="metadata"
        onLoadedMetadata={() => {
          if (videoRef.current && episode) {
            // Set initial time if episode has progress
            const initialTime = (episode.completionPercentage / 100) * totalTime;
            videoRef.current.currentTime = initialTime;
            setCurrentTime(initialTime);
          }
        }}
      >
        {/* In real implementation, this would be the actual video source */}
        <source src={episode?.videoUrl || "#"} type="video/mp4" />
      </video>

      {/* Video Display Area */}
      <div className="aspect-video relative">
        <img
          src={episode?.thumbnail || "https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=1200&h=675&fit=crop"}
          alt={episode?.title || "Anatomy education video preview"}
          className="w-full h-full object-cover"
        />

        {/* Play Overlay */}
        <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
          <button
            onClick={togglePlayPause}
            className="bg-white/90 hover:bg-white rounded-full p-4 transition-all duration-200 transform hover:scale-110"
          >
            {isPlaying ? (
              <Pause className="h-8 w-8 text-gray-900" />
            ) : (
              <Play className="h-8 w-8 text-gray-900 ml-1" />
            )}
          </button>
        </div>

        {/* Completion Badge */}
        {episode?.isCompleted && (
          <div className="absolute top-4 right-4">
            <div className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span>Completed</span>
            </div>
          </div>
        )}

        {/* Film Strip Effect */}
        <div className="absolute top-4 right-4">
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-2 transform rotate-12 shadow-lg">
            <div className="w-16 h-12 bg-white/20 rounded border-2 border-white/30"></div>
          </div>
        </div>
      </div>

      {/* Video Controls */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
        {/* Progress Bar */}
        <div className="mb-3">
          <div className="w-full bg-white/20 rounded-full h-1 cursor-pointer">
            <div
              className="bg-blue-500 h-1 rounded-full transition-all duration-200"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Control Buttons */}
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setIsPlaying(!isPlaying)}
              className="hover:bg-white/20 rounded p-1 transition-colors duration-200"
            >
              {isPlaying ? (
                <Pause className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5" />
              )}
            </button>
            <span className="text-sm font-mono">
              {formatTime(currentTime)} / {formatTime(totalTime)}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <button className="hover:bg-white/20 rounded p-1 transition-colors duration-200">
              <Volume2 className="h-5 w-5" />
            </button>
            <button className="hover:bg-white/20 rounded p-1 transition-colors duration-200">
              <Settings className="h-5 w-5" />
            </button>
            <button className="hover:bg-white/20 rounded p-1 transition-colors duration-200">
              <Maximize className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};