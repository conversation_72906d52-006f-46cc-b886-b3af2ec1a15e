import React from 'react';
import { Play, Clock, CheckCircle, Lock } from 'lucide-react';
import { cn } from '../../utils/cn';
import { Quiz } from '../../types';

interface QuizCardProps {
  quiz: Quiz;
  onClick: (quiz: Quiz) => void;
  className?: string;
}

export const QuizCard: React.FC<QuizCardProps> = ({
  quiz,
  onClick,
  className
}) => {
  const getStatusIcon = () => {
    if (!quiz.isUnlocked) {
      return <Lock className="h-5 w-5 text-gray-400" />;
    }
    if (quiz.isCompleted) {
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    }
    return <Play className="h-5 w-5 text-blue-600" />;
  };

  const getStatusText = () => {
    if (!quiz.isUnlocked) {
      return 'Complete episode to unlock';
    }
    if (quiz.isCompleted) {
      return `Completed • Score: ${quiz.score}/${quiz.questions.length}`;
    }
    return `${quiz.questions.length} questions`;
  };

  const getStatusColor = () => {
    if (!quiz.isUnlocked) {
      return 'text-gray-500';
    }
    if (quiz.isCompleted) {
      return 'text-green-600';
    }
    return 'text-blue-600';
  };

  return (
    <div
      className={cn(
        "bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden border",
        quiz.isUnlocked 
          ? "cursor-pointer hover:border-blue-200" 
          : "cursor-not-allowed border-gray-200",
        quiz.isCompleted && "bg-green-50 border-green-200",
        className
      )}
      onClick={() => quiz.isUnlocked && onClick(quiz)}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className={cn(
              "font-semibold text-lg mb-2",
              quiz.isUnlocked ? "text-gray-900" : "text-gray-500"
            )}>
              {quiz.title}
            </h3>
            {quiz.description && (
              <p className={cn(
                "text-sm",
                quiz.isUnlocked ? "text-gray-600" : "text-gray-400"
              )}>
                {quiz.description}
              </p>
            )}
          </div>
          <div className="ml-4">
            {getStatusIcon()}
          </div>
        </div>

        {/* Quiz Stats */}
        <div className="flex items-center justify-between text-sm">
          <div className={cn("flex items-center space-x-1", getStatusColor())}>
            <Clock className="h-4 w-4" />
            <span>{getStatusText()}</span>
          </div>
          
          {quiz.attempts > 0 && (
            <div className="text-gray-500">
              <span>Attempts: {quiz.attempts}</span>
              {quiz.maxAttempts && (
                <span>/{quiz.maxAttempts}</span>
              )}
            </div>
          )}
        </div>

        {/* Progress Bar for Completed Quizzes */}
        {quiz.isCompleted && quiz.score !== undefined && (
          <div className="mt-4">
            <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
              <span>Score</span>
              <span>{Math.round((quiz.score / quiz.questions.length) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(quiz.score / quiz.questions.length) * 100}%` }}
              />
            </div>
          </div>
        )}

        {/* Action Button */}
        <div className="mt-4">
          <button
            disabled={!quiz.isUnlocked}
            className={cn(
              "w-full py-2 px-4 rounded-lg font-medium transition-colors duration-200",
              quiz.isUnlocked
                ? quiz.isCompleted
                  ? "bg-green-100 text-green-700 hover:bg-green-200"
                  : "bg-blue-600 text-white hover:bg-blue-700"
                : "bg-gray-100 text-gray-400 cursor-not-allowed"
            )}
          >
            {quiz.isCompleted ? 'Review Quiz' : quiz.isUnlocked ? 'Start Quiz' : 'Locked'}
          </button>
        </div>
      </div>
    </div>
  );
};
