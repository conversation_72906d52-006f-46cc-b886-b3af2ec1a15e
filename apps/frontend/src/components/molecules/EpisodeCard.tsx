import React from 'react';
import { Play, Clock, CheckCircle } from 'lucide-react';
import { cn } from '../../utils/cn';
import { Episode } from '../../types';

interface EpisodeCardProps {
  episode: Episode;
  isCurrentlyPlaying?: boolean;
  onClick: (episode: Episode) => void;
  className?: string;
}

export const EpisodeCard: React.FC<EpisodeCardProps> = ({
  episode,
  isCurrentlyPlaying = false,
  onClick,
  className
}) => {
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div
      className={cn(
        "bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden cursor-pointer group border",
        isCurrentlyPlaying 
          ? "border-blue-500 ring-2 ring-blue-200" 
          : "border-gray-100 hover:border-blue-200",
        episode.isCompleted && "bg-green-50",
        className
      )}
      onClick={() => onClick(episode)}
    >
      {/* Thumbnail */}
      <div className="aspect-video relative overflow-hidden bg-gray-100">
        <img
          src={episode.thumbnail}
          alt={episode.title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          loading="lazy"
        />
        
        {/* Play Overlay */}
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <div className="bg-white/90 rounded-full p-3 transform scale-90 group-hover:scale-100 transition-transform duration-200">
            <Play className="h-6 w-6 text-gray-900 ml-0.5" />
          </div>
        </div>

        {/* Status Indicators */}
        <div className="absolute top-3 left-3 flex space-x-2">
          {isCurrentlyPlaying && (
            <div className="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-medium">
              Now playing
            </div>
          )}
          {episode.isCompleted && (
            <div className="bg-green-600 text-white rounded-full p-1">
              <CheckCircle className="h-4 w-4" />
            </div>
          )}
        </div>

        {/* Duration */}
        <div className="absolute bottom-3 right-3">
          <div className="bg-black/70 text-white px-2 py-1 rounded text-xs font-medium flex items-center space-x-1">
            <Clock className="h-3 w-3" />
            <span>{formatDuration(episode.duration)}</span>
          </div>
        </div>

        {/* Progress Bar */}
        {episode.completionPercentage > 0 && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/20">
            <div 
              className="h-full bg-blue-500 transition-all duration-300"
              style={{ width: `${episode.completionPercentage}%` }}
            />
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-4">
        <h3 className={cn(
          "font-semibold text-gray-900 mb-2 line-clamp-2 transition-colors duration-200",
          isCurrentlyPlaying ? "text-blue-600" : "group-hover:text-blue-600"
        )}>
          {episode.title}
        </h3>
        
        <p className="text-gray-600 text-sm line-clamp-2 leading-relaxed">
          {episode.description}
        </p>
      </div>
    </div>
  );
};
