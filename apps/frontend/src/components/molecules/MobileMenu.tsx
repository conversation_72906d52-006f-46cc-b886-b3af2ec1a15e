import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, ChevronUp, X, User } from 'lucide-react';
import { cn } from '../../utils/cn';
import { Button } from '../atoms/Button';
import { NavigationItem } from '../../types';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  navigationItems: NavigationItem[];
  isLoggedIn?: boolean;
  userEmail?: string;
}

interface SubMenuItem {
  label: string;
  href: string;
}

const subMenuItems: Record<string, SubMenuItem[]> = {
  'LEARN': [
    { label: 'Full-length lessons', href: '#full-lessons' },
    { label: 'Mini lessons', href: '/map' },
    { label: 'Course Library', href: '#course-library' },
    { label: 'Study Tools', href: '#study-tools' }
  ],
  'EXPLORE': [
    { label: 'How it works', href: '#how-it-works' },
    { label: 'Pricing', href: '#pricing' },
    { label: 'About', href: '#about' },
    { label: 'Progress Tracking', href: '#progress' }
  ]
};

export const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  onClose,
  navigationItems,
  isLoggedIn = false,
  userEmail = "<EMAIL>"
}) => {
  const [expandedItem, setExpandedItem] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Handle escape key and outside clicks
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    const handleClickOutside = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.addEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const toggleSubMenu = (itemLabel: string) => {
    setExpandedItem(expandedItem === itemLabel ? null : itemLabel);
  };

  const handleLinkClick = (href?: string) => {
    if (href && href.startsWith('/')) {
      window.location.href = href;
    }
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 z-40 md:hidden"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Mobile Menu - Full Width and Height */}
      <div
        ref={menuRef}
        className={cn(
          "fixed inset-0 bg-white z-50 md:hidden flex flex-col",
          "transform transition-transform duration-300 ease-in-out",
          isOpen ? "translate-x-0" : "translate-x-full"
        )}
        role="dialog"
        aria-modal="true"
        aria-label="Mobile navigation menu"
      >
        {/* Header - Fixed */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
          <span className="text-lg font-semibold text-gray-900">Menu</span>
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors duration-200"
            aria-label="Close menu"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Scrollable Menu Content */}
        <div className="flex-1 overflow-y-auto">
          <nav className="py-4">
            <ul className="space-y-1 px-4">
              {navigationItems.map((item) => (
                <li key={item.label}>
                  {item.hasDropdown ? (
                    <div>
                      <button
                        onClick={() => toggleSubMenu(item.label)}
                        className="flex items-center justify-between w-full px-4 py-4 text-left text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-all duration-200 font-medium text-lg"
                        aria-expanded={expandedItem === item.label}
                        aria-controls={`submenu-${item.label}`}
                      >
                        <span>{item.label}</span>
                        {expandedItem === item.label ? (
                          <ChevronUp className="h-5 w-5 transition-transform duration-200" />
                        ) : (
                          <ChevronDown className="h-5 w-5 transition-transform duration-200" />
                        )}
                      </button>
                      
                      {/* Sub-menu */}
                      <div
                        id={`submenu-${item.label}`}
                        className={cn(
                          "overflow-hidden transition-all duration-300 ease-in-out",
                          expandedItem === item.label 
                            ? "max-h-96 opacity-100" 
                            : "max-h-0 opacity-0"
                        )}
                      >
                        <ul className="ml-6 mt-2 space-y-1 border-l-2 border-blue-100 pl-4">
                          {subMenuItems[item.label]?.map((subItem) => (
                            <li key={subItem.label}>
                              <a
                                href={subItem.href}
                                onClick={() => handleLinkClick(subItem.href)}
                                className="block px-4 py-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-all duration-200"
                              >
                                {subItem.label}
                              </a>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ) : (
                    <a
                      href={item.href}
                      onClick={() => handleLinkClick(item.href)}
                      className="block px-4 py-4 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-all duration-200 font-medium text-lg"
                    >
                      {item.label}
                    </a>
                  )}
                </li>
              ))}
            </ul>
          </nav>
        </div>

        {/* Footer with Auth/Account Section - Fixed at Bottom */}
        <div className="border-t border-gray-200 p-4 space-y-3 flex-shrink-0 bg-white">
          {isLoggedIn ? (
            <div className="space-y-3">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <User className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">My Account</p>
                  <p className="text-xs text-gray-500 truncate">{userEmail}</p>
                </div>
              </div>
              <div className="space-y-2">
                <a
                  href="#profile"
                  onClick={() => handleLinkClick()}
                  className="block w-full text-left px-4 py-3 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                >
                  Profile Settings
                </a>
                <a
                  href="#subscription"
                  onClick={() => handleLinkClick()}
                  className="block w-full text-left px-4 py-3 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                >
                  Subscription
                </a>
                <a
                  href="#progress"
                  onClick={() => handleLinkClick()}
                  className="block w-full text-left px-4 py-3 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                >
                  My Progress
                </a>
                <button
                  onClick={() => handleLinkClick()}
                  className="block w-full text-left px-4 py-3 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200"
                >
                  Sign Out
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <Button 
                variant="outline" 
                size="md" 
                className="w-full justify-center py-3 text-base font-medium"
                onClick={() => handleLinkClick()}
              >
                LOG IN
              </Button>
              <Button 
                variant="secondary" 
                size="md" 
                className="w-full justify-center py-3 text-base font-medium"
                onClick={() => handleLinkClick()}
              >
                SIGN UP
              </Button>
            </div>
          )}
        </div>
      </div>
    </>
  );
};