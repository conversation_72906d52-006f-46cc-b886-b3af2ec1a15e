import Fastify from 'fastify';
import cors from '@fastify/cors';

const fastify = Fastify({
  logger: true
});

// Register CORS plugin
await fastify.register(cors, {
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
});

// Health check endpoint
fastify.get('/health', async (request, reply) => {
  return { status: 'ok', timestamp: new Date().toISOString() };
});

// API Routes
fastify.get('/api/anatomy/regions', async (request, reply) => {
  return {
    success: true,
    data: [
      {
        id: 'head-neck',
        name: 'Head & Neck',
        description: 'Explore the complex anatomy of the head and neck region',
        videoCount: 24,
        completionRate: 0
      },
      {
        id: 'thorax',
        name: 'Thorax',
        description: 'Learn about the chest cavity and its vital organs',
        videoCount: 18,
        completionRate: 0
      },
      {
        id: 'abdomen',
        name: 'Abdomen',
        description: 'Discover the abdominal organs and their functions',
        videoCount: 22,
        completionRate: 0
      },
      {
        id: 'pelvis',
        name: '<PERSON><PERSON><PERSON>',
        description: 'Study the pelvic region and reproductive anatomy',
        videoCount: 16,
        completionRate: 0
      },
      {
        id: 'upper-limb',
        name: 'Upper Limb',
        description: 'Master the anatomy of arms, shoulders, and hands',
        videoCount: 20,
        completionRate: 0
      },
      {
        id: 'lower-limb',
        name: 'Lower Limb',
        description: 'Understand the structure of legs, hips, and feet',
        videoCount: 19,
        completionRate: 0
      }
    ]
  };
});

// Get specific region details
fastify.get('/api/anatomy/regions/:regionId', async (request, reply) => {
  const { regionId } = request.params as { regionId: string };
  
  // Mock data for demonstration
  const regionData = {
    id: regionId,
    name: regionId.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' '),
    description: `Detailed study of the ${regionId} region`,
    videos: [
      {
        id: `${regionId}-intro`,
        title: `Introduction to ${regionId}`,
        duration: 420,
        thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
        completed: false
      }
    ],
    quizzes: [
      {
        id: `${regionId}-quiz-1`,
        title: `${regionId} Basic Quiz`,
        questionCount: 10,
        completed: false
      }
    ]
  };

  return {
    success: true,
    data: regionData
  };
});

// User progress endpoint
fastify.get('/api/user/progress', async (request, reply) => {
  return {
    success: true,
    data: {
      totalVideosWatched: 0,
      totalQuizzesCompleted: 0,
      currentStreak: 0,
      badges: [],
      overallProgress: 0
    }
  };
});

// Start server
const start = async () => {
  try {
    const port = process.env.PORT ? parseInt(process.env.PORT) : 3001;
    const host = process.env.HOST || '0.0.0.0';
    
    await fastify.listen({ port, host });
    console.log(`🚀 Backend server running on http://localhost:${port}`);
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start();