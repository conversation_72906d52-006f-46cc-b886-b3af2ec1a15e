import Fastify from 'fastify';
import cors from '@fastify/cors';

const fastify = Fastify({
  logger: true
});

// Register CORS plugin
await fastify.register(cors, {
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
});

// Health check endpoint
fastify.get('/health', async (request, reply) => {
  return { status: 'ok', timestamp: new Date().toISOString() };
});

// API Routes
fastify.get('/api/anatomy/regions', async (request, reply) => {
  return {
    success: true,
    data: [
      {
        id: 'head-neck',
        name: 'Head & Neck',
        description: 'Explore the complex anatomy of the head and neck region',
        videoCount: 24,
        completionRate: 0
      },
      {
        id: 'thorax',
        name: 'Thorax',
        description: 'Learn about the chest cavity and its vital organs',
        videoCount: 18,
        completionRate: 0
      },
      {
        id: 'abdomen',
        name: 'Abdomen',
        description: 'Discover the abdominal organs and their functions',
        videoCount: 22,
        completionRate: 0
      },
      {
        id: 'pelvis',
        name: '<PERSON><PERSON><PERSON>',
        description: 'Study the pelvic region and reproductive anatomy',
        videoCount: 16,
        completionRate: 0
      },
      {
        id: 'upper-limb',
        name: 'Upper Limb',
        description: 'Master the anatomy of arms, shoulders, and hands',
        videoCount: 20,
        completionRate: 0
      },
      {
        id: 'lower-limb',
        name: 'Lower Limb',
        description: 'Understand the structure of legs, hips, and feet',
        videoCount: 19,
        completionRate: 0
      }
    ]
  };
});

// Get specific region details
fastify.get('/api/anatomy/regions/:regionId', async (request, reply) => {
  const { regionId } = request.params as { regionId: string };
  
  // Mock data for demonstration
  const regionData = {
    id: regionId,
    name: regionId.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' '),
    description: `Detailed study of the ${regionId} region`,
    videos: [
      {
        id: `${regionId}-intro`,
        title: `Introduction to ${regionId}`,
        duration: 420,
        thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
        completed: false
      }
    ],
    quizzes: [
      {
        id: `${regionId}-quiz-1`,
        title: `${regionId} Basic Quiz`,
        questionCount: 10,
        completed: false
      }
    ]
  };

  return {
    success: true,
    data: regionData
  };
});

// User progress endpoint
fastify.get('/api/user/progress', async (request, reply) => {
  return {
    success: true,
    data: {
      totalVideosWatched: 0,
      totalQuizzesCompleted: 0,
      currentStreak: 0,
      badges: [],
      overallProgress: 0
    }
  };
});

// Get lesson details with episodes, quizzes, and memorization card
fastify.get('/api/lessons/:lessonId', async (request, reply) => {
  const { lessonId } = request.params as { lessonId: string };

  // Mock lesson data - in real implementation, fetch from database
  const mockLesson = {
    id: lessonId,
    regionId: 'cranial-cavity',
    title: 'Lesson 1: King Idols Tomb',
    description: 'Master 12 cranial nerves through 6 focused episodes',
    thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    episodes: [
      {
        id: `${lessonId}-episode-1`,
        title: 'Episode 1: Introduction to Cranial Nerves',
        description: 'Overview of the 12 cranial nerves and their functions',
        thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
        duration: 180, // 3 minutes
        vimeoId: 'mock-vimeo-id-1',
        isCompleted: false,
        completionPercentage: 0,
        isCurrentlyPlaying: false
      },
      {
        id: `${lessonId}-episode-2`,
        title: 'Episode 2: Olfactory and Optic Nerves',
        description: 'Deep dive into cranial nerves I and II',
        thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
        duration: 240, // 4 minutes
        vimeoId: 'mock-vimeo-id-2',
        isCompleted: false,
        completionPercentage: 0,
        isCurrentlyPlaying: false
      },
      {
        id: `${lessonId}-episode-3`,
        title: 'Episode 3: Oculomotor and Trochlear Nerves',
        description: 'Understanding cranial nerves III and IV',
        thumbnail: 'https://images.pexels.com/photos/1172253/pexels-photo-1172253.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
        duration: 210, // 3.5 minutes
        vimeoId: 'mock-vimeo-id-3',
        isCompleted: false,
        completionPercentage: 0,
        isCurrentlyPlaying: false
      }
    ],
    quizzes: [
      {
        id: `${lessonId}-quiz-1`,
        episodeId: `${lessonId}-episode-1`,
        title: 'Episode 1 Quiz: Cranial Nerve Basics',
        description: 'Test your understanding of cranial nerve fundamentals',
        questions: [
          {
            id: 'q1',
            type: 'multiple-choice',
            question: 'How many cranial nerves are there in total?',
            options: ['10', '12', '14', '16'],
            correctAnswer: '12',
            explanation: 'There are 12 pairs of cranial nerves that emerge directly from the brain.',
            difficulty: 'easy'
          },
          {
            id: 'q2',
            type: 'multiple-choice',
            question: 'Which cranial nerve is responsible for smell?',
            options: ['Optic nerve', 'Olfactory nerve', 'Trigeminal nerve', 'Facial nerve'],
            correctAnswer: 'Olfactory nerve',
            explanation: 'The olfactory nerve (cranial nerve I) is responsible for the sense of smell.',
            difficulty: 'medium'
          }
        ],
        isCompleted: false,
        isUnlocked: false, // Unlocked when episode 1 is completed
        score: 0,
        attempts: 0,
        maxAttempts: 3
      },
      {
        id: `${lessonId}-quiz-2`,
        episodeId: `${lessonId}-episode-2`,
        title: 'Episode 2 Quiz: Olfactory and Optic',
        description: 'Quiz on cranial nerves I and II',
        questions: [
          {
            id: 'q3',
            type: 'multiple-choice',
            question: 'The optic nerve is cranial nerve number:',
            options: ['I', 'II', 'III', 'IV'],
            correctAnswer: 'II',
            explanation: 'The optic nerve is cranial nerve II, responsible for vision.',
            difficulty: 'easy'
          }
        ],
        isCompleted: false,
        isUnlocked: false, // Unlocked when episode 2 is completed
        score: 0,
        attempts: 0,
        maxAttempts: 3
      }
    ],
    memorizationCard: {
      id: `${lessonId}-memory-card`,
      lessonId: lessonId,
      title: 'Cranial Nerves Memory Card',
      description: 'Interactive visual summary of all 12 cranial nerves',
      thingLinkUrl: 'https://www.thinglink.com/scene/mock-cranial-nerves-card',
      isUnlocked: false // Unlocked when all episodes and quizzes are completed
    },
    totalDuration: 630, // Sum of all episode durations
    episodeCount: 3,
    completionRate: 0,
    isCompleted: false,
    status: {
      allEpisodesCompleted: false,
      allQuizzesCompleted: false,
      memorizationCardUnlocked: false,
      memorizationCardCompleted: false,
      masteryAchieved: false
    }
  };

  return {
    success: true,
    data: mockLesson
  };
});

// Get specific quiz data
fastify.get('/api/quizzes/:quizId', async (request, reply) => {
  const { quizId } = request.params as { quizId: string };

  // Mock quiz data - in real implementation, fetch from database
  const mockQuiz = {
    id: quizId,
    episodeId: 'lesson-1-episode-1',
    title: 'Episode 1 Quiz: Cranial Nerve Basics',
    description: 'Test your understanding of cranial nerve fundamentals',
    questions: [
      {
        id: 'q1',
        type: 'multiple-choice',
        question: 'How many cranial nerves are there in total?',
        options: ['10', '12', '14', '16'],
        correctAnswer: '12',
        explanation: 'There are 12 pairs of cranial nerves that emerge directly from the brain.',
        difficulty: 'easy'
      },
      {
        id: 'q2',
        type: 'multiple-choice',
        question: 'Which cranial nerve is responsible for smell?',
        options: ['Optic nerve', 'Olfactory nerve', 'Trigeminal nerve', 'Facial nerve'],
        correctAnswer: 'Olfactory nerve',
        explanation: 'The olfactory nerve (cranial nerve I) is responsible for the sense of smell.',
        difficulty: 'medium'
      },
      {
        id: 'q3',
        type: 'multiple-choice',
        question: 'The optic nerve is cranial nerve number:',
        options: ['I', 'II', 'III', 'IV'],
        correctAnswer: 'II',
        explanation: 'The optic nerve is cranial nerve II, responsible for vision.',
        difficulty: 'easy'
      }
    ],
    isCompleted: false,
    isUnlocked: true,
    score: 0,
    attempts: 0,
    maxAttempts: 3
  };

  return {
    success: true,
    data: mockQuiz
  };
});

// Submit quiz answers
fastify.post('/api/quizzes/:quizId/submit', async (request, reply) => {
  const { quizId } = request.params as { quizId: string };
  const { answers } = request.body as { answers: Record<string, string> };

  // Mock quiz scoring - in real implementation, validate against database
  const correctAnswers = {
    'q1': '12',
    'q2': 'Olfactory nerve',
    'q3': 'II'
  };

  let score = 0;
  const totalQuestions = Object.keys(correctAnswers).length;

  Object.entries(answers).forEach(([questionId, answer]) => {
    if (correctAnswers[questionId as keyof typeof correctAnswers] === answer) {
      score++;
    }
  });

  const percentage = Math.round((score / totalQuestions) * 100);
  const passed = percentage >= 70; // 70% passing grade

  return {
    success: true,
    data: {
      quizId,
      score,
      totalQuestions,
      percentage,
      passed,
      answers,
      completedAt: new Date().toISOString()
    }
  };
});

// Update episode progress (video completion tracking)
fastify.post('/api/episodes/:episodeId/progress', async (request, reply) => {
  const { episodeId } = request.params as { episodeId: string };
  const { watchedDuration, totalDuration, completionPercentage } = request.body as {
    watchedDuration: number;
    totalDuration: number;
    completionPercentage: number;
  };

  const isCompleted = completionPercentage >= 100;

  // Mock progress update - in real implementation, save to database
  const progressData = {
    episodeId,
    watchedDuration,
    totalDuration,
    completionPercentage,
    isCompleted,
    completedAt: isCompleted ? new Date().toISOString() : undefined,
    lastWatchedAt: new Date().toISOString()
  };

  return {
    success: true,
    data: progressData
  };
});

// Get user progress for a specific lesson
fastify.get('/api/lessons/:lessonId/progress', async (request, reply) => {
  const { lessonId } = request.params as { lessonId: string };

  // Mock progress data - in real implementation, fetch from database
  const mockProgress = {
    userId: 'mock-user-id',
    lessonId,
    episodeProgress: {
      [`${lessonId}-episode-1`]: {
        episodeId: `${lessonId}-episode-1`,
        watchedDuration: 180,
        totalDuration: 180,
        completionPercentage: 100,
        isCompleted: true,
        completedAt: '2025-01-23T10:00:00Z',
        lastWatchedAt: '2025-01-23T10:00:00Z'
      },
      [`${lessonId}-episode-2`]: {
        episodeId: `${lessonId}-episode-2`,
        watchedDuration: 120,
        totalDuration: 240,
        completionPercentage: 50,
        isCompleted: false,
        lastWatchedAt: '2025-01-23T11:00:00Z'
      }
    },
    quizProgress: {
      [`${lessonId}-quiz-1`]: {
        quizId: `${lessonId}-quiz-1`,
        isCompleted: true,
        score: 2,
        attempts: 1,
        completedAt: '2025-01-23T10:05:00Z',
        answers: {
          'q1': '12',
          'q2': 'Olfactory nerve'
        }
      }
    },
    overallCompletionPercentage: 60,
    lastUpdated: '2025-01-23T11:00:00Z'
  };

  return {
    success: true,
    data: mockProgress
  };
});

// Update memorization card progress
fastify.post('/api/memorization-cards/:cardId/progress', async (request, reply) => {
  const { cardId } = request.params as { cardId: string };
  const { timeSpent, isCompleted } = request.body as {
    timeSpent: number;
    isCompleted: boolean;
  };

  // Mock progress update - in real implementation, save to database
  const progressData = {
    cardId,
    isCompleted,
    completedAt: isCompleted ? new Date().toISOString() : undefined,
    timeSpent
  };

  return {
    success: true,
    data: progressData
  };
});

// Get lesson status (for unlock logic)
fastify.get('/api/lessons/:lessonId/status', async (request, reply) => {
  const { lessonId } = request.params as { lessonId: string };

  // Mock status calculation - in real implementation, calculate from database
  const mockStatus = {
    allEpisodesCompleted: false,
    allQuizzesCompleted: false,
    memorizationCardUnlocked: false,
    memorizationCardCompleted: false,
    masteryAchieved: false,
    lastAccessedAt: '2025-01-23T11:00:00Z'
  };

  return {
    success: true,
    data: mockStatus
  };
});

// Start server
const start = async () => {
  try {
    const port = process.env.PORT ? parseInt(process.env.PORT) : 3001;
    const host = process.env.HOST || '0.0.0.0';
    
    await fastify.listen({ port, host });
    console.log(`🚀 Backend server running on http://localhost:${port}`);
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start();