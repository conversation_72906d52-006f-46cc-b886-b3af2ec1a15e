{"name": "@anatomy-land/backend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint src --ext .ts"}, "dependencies": {"@fastify/cors": "^9.0.1", "fastify": "^4.24.3"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "tsx": "^4.6.0", "typescript": "^5.3.2"}}