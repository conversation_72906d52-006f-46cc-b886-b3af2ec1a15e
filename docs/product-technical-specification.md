# 📄 Anatomy Land - Version 2

## 📘 1. Product Requirements Document (PRD)

**Project Title**: _Anatomy Land V2 - Micro-Learning Platform_  
**Prepared by**: _<PERSON>_  
**Date**: _2025-01-23_

---

### 1.1. Overview
This web application is a next-generation video and visual memorization tool designed for medical students (MD, PhD, PA, PT) to accelerate learning anatomy coursework through micro-learning approaches. Built to address low adoption rates of traditional long-form video content, it serves medical students through an interactive map interface that mirrors human anatomical regions, featuring bite-sized mini-videos, TikTok-style reels, quizzes, and progress tracking to enhance anatomy learning retention.

**Legacy Integration**: This application will integrate with the existing Milos Academy platform (anatomyland.com) for authentication, subscription management, and blog content, while eventually becoming the primary learning platform at app.anatomyland.com.

---

### 1.2. Goals & Objectives
- **Address Adoption Challenge**: Transform long-form video content into engaging micro-learning experiences
- Enable medical students to learn anatomy through visual memorization techniques and bite-sized content
- Provide an interactive map interface with 6 themed anatomical regions
- Deliver multiple content formats: Full-length videos (Future), Mini-videos, and TikTok-style Reels
- Offer comprehensive learning tools: videos, memorization cards, quizzes, and exam prep
- Ensure responsive design for optimal learning across desktop and mobile devices
- Maintain seamless integration with legacy Milos Academy platform
- Create pathway for legacy site to transition to marketing-focused landing page

---

### 1.3. User Personas
- **Medical Students (Primary)**: MD, PhD, PA, PT students seeking efficient, engaging anatomy learning
- **Legacy Users (Migration)**: Existing anatomyland.com users transitioning to new micro-learning format
- **Mobile-First Learners**: Students preferring short-form, social media-style educational content
- **Instructors/Educators (Secondary - Future)**: May monitor student progress and recommend content
- **Study Group Leaders (Potential - Future)**: Students who coordinate group learning sessions

---

### 1.4. Key Use Cases / Scenarios
- A medical student logs in and navigates the interactive anatomical map with 6 themed regions
- Student clicks on "Cranial Carnival" region and chooses between Full-length, Minis, or Reels format
- Student watches short TikTok-style reels during study breaks or commuting
- Student completes a mini-video episode and proceeds to memorization cards for active recall
- Student takes region-specific quizzes with immediate feedback and explanations
- Student tracks learning progress across all 6 anatomical regions and content formats
- Legacy user seamlessly logs in using existing anatomyland.com credentials
- Student accesses subscription details by being redirected to legacy account management

---

### 1.5. Success Metrics
**Note: The following metrics are preliminary estimates intended for strategic discussion and goal-setting purposes. These numbers are not final commitments and should be validated through user research, A/B testing, and iterative measurement once the platform is launched.**

- **Engagement Improvement**: >50% increase in session completion rates vs. legacy platform
- **Content Consumption**: Average 3-5 mini-videos or 10+ reels per session
- **Learning Retention**: >75% quiz score improvement after content completion
- **User Adoption**: 70% weekly active users (significant improvement from legacy)
- **Mobile Usage**: >60% of sessions from mobile devices (reels-focused)
- **Content Completion**: >80% completion rate for mini-videos vs. legacy long-form content
- **Student Satisfaction**: NPS score >60 within 3 months of launch

*These metrics will be refined based on baseline measurements from the legacy platform and early user feedback from the new micro-learning platform.*

---

## 🧩 2. Functional Requirements

### 2.1. User Registration & Authentication

**User Stories:**
- As a legacy user, I can log in using my existing anatomyland.com credentials, so that I don't need to create a new account or lose my existing data
- As a new user, I can register with email and password, so that I can access the micro-learning platform
- As a user, I can reset my forgotten password, so that I can regain access to my account
- As a user, I can stay logged in across sessions, so that I don't have to repeatedly enter my credentials
- As a user, I can securely log out, so that my account remains protected on shared devices

**Technical Requirements:**
- **Legacy Integration**: Seamless authentication using existing anatomyland.com credentials
- Students can register with email and password (new users)
- Authentication supports session management and password recovery

**Playback Access Rule:** All in-app video content (mini-videos and reels) requires an authenticated session. Unauthenticated users attempting playback are redirected to the **Log In / Sign Up** flow.

---

### 2.2. Interactive Map System

**User Stories:**
- As a student, I can view the interactive anatomical map with 6 themed regions, so that I can choose which body system to study
- As a student, I can click on "Cranial Carnival" region, so that I can access head & neck anatomy content
- As a student, I can see my progress on each region visually, so that I know which areas I've completed and which need attention
- As a mobile user, I can navigate the map easily on my phone, so that I can study on-the-go
- As a student, I can see the learning format options (Full-length - redirect to legacy/Minis/Reels) when I click a region, so that I can choose my preferred study method

**Technical Requirements:**
- Visual anatomical map interface displaying **6 themed regions**:
  1. **"Cranial Carnival"** - Head & Neck anatomy
  2. **"Space Pirate Chest"** - Thoracic anatomy  
  3. **"Haunted Arm Farm"** - Upper extremity anatomy
  4. **"I-GU-azu Falls"** - Genitourinary system
  5. **"Roman LEG-ends"** - Lower extremity anatomy
  6. **"GI-Manji"** - Gastrointestinal system
- Clickable regions with visual progress indicators
- **Learning Format Modal**: When region is clicked, users choose:
  - **Full-length**: Complete lesson in one session
  - **Minis**: Bite-sized 2-3 minute segments
  - **Reels**: Quick, focused highlights under 1 minute
- Responsive map design for mobile and desktop viewing
- Visual completion tracking per region and format type

---

### 2.3. Content Management & Hierarchy

**User Stories:**
- As a student, I can browse episodes/courses within a region, so that I can see all available content for that body system
- As a student, I can access individual series/chapters within an episode, so that I can learn specific anatomical concepts in detail
- As a student, I can view standalone reels, so that I can do quick reviews without committing to longer content
- As a student, I can see content organized by anatomical region, so that I can focus on specific areas of study
- As a student, I can access legacy full-length videos when needed, so that I can get comprehensive coverage of topics
- As a content manager, I can organize videos in Vimeo by region and lesson folders, so that content is properly structured for the application

**Technical Requirements:**
**Three-Tier Content Structure:**
- **Regions** (6 total) → **Episodes/Courses** → **Series/Chapters**
- **Plus**: Standalone **Reels** for quick consumption
- Content categorization by anatomical region and learning format
- **Vimeo Integration**: Access to video library via Vimeo API
- **Vimeo Folder Structure**: 
  - Root → Region Folders → Lesson Folders → Video Files (Minis/Reels)
  - Example: `/Cranial-Carnival/Lesson-1-King-Idols-Tomb/minis/` or `/reels/`
- Content delivery optimization for different formats (mini, reels)
- **Content Discovery**: Automated video detection and categorization from Vimeo folder structure

**Additional User Stories (Lesson Page Navigation):**
- As a student, I can switch between "Episodes", "Quizzes", and "Memory Card" tabs on the lesson page, so that I can quickly access the type of content I need.
- As a desktop user, I can navigate a horizontal carousel of episode cards using previous/next arrows, so that I can browse all mini-videos of the lesson.
- As a mobile user, I can swipe the carousel of episode cards, so that I can easily browse content on my phone.
- As a student, the episode card currently playing is marked "Now playing", so that I always know my position in the lesson.
- As a student, I can see runtime labels on each episode card, so that I can plan my study time.

**Technical Enhancements:**
- **Carousel Library**: Use Swiper.js (or similar) with mouse-wheel, arrow, and touch support.
- **Responsive Layout**: 1–4 cards per view depending on viewport size; lazy-load card thumbnails.
- **Tab Component**: Controlled React state; deep-link support with URL hash (`?tab=quizzes`).
- **Accessibility**: Arrow key navigation and ARIA roles for carousel & tabs.

---

### 2.4. Video Content System

**User Stories:**
- As a student, I can watch 2-5 minute mini-videos, so that I can learn anatomy concepts in digestible segments
- As a student, I can watch TikTok-style reels under 1 minute, so that I can do quick reviews during short breaks
- As a mobile user, I can swipe through reels, so that I can quickly consume multiple pieces of content
- As a student, I can control video playback (play, pause, rewind, speed), so that I can learn at my own pace
- As a student, I can bookmark my place in videos, so that I can resume where I left off
- As a student, I can be redirected to the legacy site for full-length videos, so that I can access comprehensive lessons when needed
- As a student, I can have videos load quickly on my mobile device, so that I don't waste time waiting
- As a content manager, I can upload and organize videos in Vimeo, so that they automatically appear in the correct sections of the application

**Technical Requirements:**
**Provider-Agnostic Video Delivery:**
- **Video Player Framework**: [Video.js](https://videojs.com/) open-source HTML5 player for all in-app playback[[1]](https://videojs.com/)
- **Vimeo Support**: Use the official `videojs-vimeo` plugin to stream Vimeo URLs through Video.js (maintains flexibility to swap providers later)
- **Multiple Source Support**: Player accepts HLS/DASH/MP4 URLs from any CDN or hosting service
- **Mini-Videos**: 2-5 minute segments delivered via HLS (preferred) or MP4
- **Reels**: <1 minute clips delivered via HLS or MP4
- **Full-length Videos**: Redirect to legacy anatomyland.com platform
- **Player Features**:
  - Standard controls (play, pause, rewind, speed control)
  - Reels UX: swipe / auto-advance using Video.js playlist & carousel plugins
  - Bookmarking & progress tracking via Video.js events (`timeupdate`, `ended`)
  - Custom skin matching Anatomy Land branding
- **Streaming Quality**: Adaptive bitrate (HLS/DASH) supported by Video.js across devices
- **Mobile Optimization**: Responsive player, gesture controls, poster images for fast load
- **Accessibility**: Caption tracks (VTT) loaded via Video.js tracks API

**Integration Workflow:**
1. Back-end retrieves secure playback URLs from Vimeo API (or future provider)
2. Front-end passes source list to Video.js player component
3. Player initializes with provider-specific plugin (e.g., videojs-vimeo) when needed
4. Analytics events captured via Video.js event hooks and relayed to back-end

**Additional User Stories:**
- As an unauthenticated visitor, I am prompted to log in or sign up when I attempt to play any mini-video or reel, so that premium educational content remains secure.
- As a student, I can view reels in a full-screen modal carousel (TikTok-style), so that I can swipe vertically or horizontally to the next piece of content without page reloads.
- As a student, when a reel has an associated quiz, the quiz automatically launches immediately after the reel finishes, so that I can test my knowledge in the same flow.
- As a student, after completing the quick quiz, the next reel auto-plays, so that my study session remains seamless.

**Technical Enhancements:**
- **Authentication Guard**: Front-end route guard and back-end middleware ensure video source URLs are provided only to authenticated requests.
- **Reel Modal Player**: Video.js instance inside a modal; `videojs-playlist-ui` for carousel thumbnail navigation; swipe support via touch events.
- **Quiz Integration**: Front-end listens to `ended` event on Video.js. If `nextItem.type === 'quiz'`, open quiz component instead of another video; upon quiz completion emit `quizComplete` to resume playlist.
- **Playlist Sequence Model**: Backend `/api/content/reels/:lessonId` returns ordered array combining reels and quizzes: `[ {type:'video', src:...}, {type:'quiz', quizId:...}, ... ]`.

---

### 2.5. Learning Tools & Features

**User Stories:**
- As a student, I can experience memorable storytelling in videos, so that I can better remember anatomical concepts through narrative
- As a student, I can use interactive memorization cards, so that I can practice active recall of anatomical structures
- As a student, I can take review quizzes with immediate feedback, so that I can test my understanding and learn from mistakes
- As a student, I can practice with clinical scenario-based exam prep questions, so that I can prepare for real-world medical situations
- As a student, I can see detailed explanations for quiz answers, so that I understand why an answer is correct or incorrect
- As a student, I can retake quizzes with different questions, so that I can reinforce my learning
- As a student, I can receive adaptive questioning based on my performance, so that I focus on areas where I need improvement

**Technical Requirements:**
**Four Core Learning Components:**
1. **Memorable Storytelling**: Narrative-driven video content with characters
2. **Memorization Cards**: Interactive flashcards for active recall
3. **Review Quizzes**: Comprehensive assessments with immediate feedback
4. **Exam Prep**: Clinical scenario-based questions (15 questions per region)

**Quiz System Features:**
- Multiple question types (multiple choice, drag-and-drop, labeling)
- 30+ questions per region across different difficulty levels
- Immediate feedback with detailed explanations
- Adaptive questioning based on performance
- Progress tracking and weak area identification

**Additional User Stories (Quiz Flow):**
- As a student, I can view a list of quizzes in carousel form under the "Quizzes" tab, so that I can pick which quiz to take.
- As a student, completed quizzes show a green completion badge, so that I can track my progress.
- As a student, when I start a quiz, the page scrolls to a full-width quiz panel with question progress (e.g., "Question 1 of 5"), so that I stay focused.
- As a student, after selecting an answer and clicking "Check", I receive immediate feedback and can proceed to the next question.
- As a student, I can exit a quiz mid-way and return to the quiz carousel without losing other lesson context.
- As a mobile user, the quiz panel adapts to narrow view with vertical layout, so that it remains readable.

**Additional User Stories (Memorization Card):**
- As a student, I can open the "Memory Card" tab after completing all quizzes, so that I can study the summary card.
- As a student, the memorization card opens in an iframe modal that fills the viewport, so that I can interact with ThingLink hotspots without distractions.
- As a student, I can navigate hotspots inside the ThingLink card, so that I can explore key facts visually.
- As a student, I can close the memorization card modal and return to the lesson page, so that I can continue studying other lessons.

**Technical Requirements Addendum:**
- **Quiz Engine**: React component with progress bar, radio options, validation, `Check` & `Exit` buttons; question data supplied by `/api/quiz/:quizId`.
- **Adaptive Layout**: CSS Grid/Flexbox to switch between desktop two-column and mobile single-column quiz UI.
- **Memorization Card Embed**: ThingLink iframe with responsive `aspect-ratio`; fullscreen mode enabled via ThingLink player API.
- **Unlock Logic**: Memory Card tab remains disabled until all quizzes and episodes in the lesson are marked complete; back-end returns `lessonStatus` object to drive UI state.
- **Progress Events**: Completion of each quiz emits `quizComplete`; when all quizzes complete emit `lessonQuizzesComplete` to unlock Memory Card.

---

### 2.6. Progress Tracking & Analytics

**User Stories:**
- As a student, I can see my completion progress across all content formats (Full-length, Minis, Reels), so that I know what I've accomplished
- As a student, I can view my progress on the 6-region map, so that I can see which body systems I've mastered
- As a student, I can see my quiz performance history, so that I can track my improvement over time
- As a student, I can see analytics on my preferred learning formats, so that I can understand my study patterns
- As a student, I can receive recommendations based on my weak areas, so that I can focus my study efforts effectively
- As a legacy user, I can see my existing progress imported from anatomyland.com, so that I don't lose my learning history
- As a student, I can see time spent studying each region, so that I can balance my study schedule

**Technical Requirements:**
- **Multi-Format Progress**: Track completion across Full-length, Minis, and Reels
- Individual student dashboard with regional breakdowns
- **6-Region Progress Map**: Visual completion status for each themed region
- Learning analytics: time spent, preferred formats, performance trends
- **Legacy Data Integration**: Import existing progress from anatomyland.com
- Recommendations based on learning patterns and weak areas

---

### 2.7. Legacy Platform Integration

**User Stories:**
- As a legacy user, I can log in using my existing anatomyland.com account, so that I have seamless access to the new platform
- As a user, I can be redirected to the legacy platform for subscription management, so that I can handle billing and account details
- As a user, I can access blog content from the legacy site, so that I can read additional educational material
- As a user, I can have my progress synchronized between platforms, so that my learning data is consistent (**Potential**)
- As a legacy user, I can transition to the new platform without losing my preferences or history, so that the migration is smooth and worry-free (**Potential**)

**Technical Requirements:**
**Critical Integration Points:**
- **Authentication**: Single sign-on with existing anatomyland.com accounts
- **Subscription Management**: Redirect to legacy platform for account/billing
- **Blog Content**: Link to existing blog posts on legacy site
- **User Data Sync**: Progress and preferences synchronization
- **Gradual Migration Path**: Seamless transition for existing users

---

## ⚙️ 3. Technical Constraints / Assumptions

### 3.1. Tech Stack (Current Implementation)
- **Frontend**: React 18 with TypeScript, Vite build tool
- **Styling**: TailwindCSS for responsive design
- **Icons**: Lucide React  
- **Architecture**: Atomic Design Pattern
- **Backend**: Fastify with TypeScript
- **Database**: PostgreSQL (for new user data and progress, the legacy system use MYSQL)
- **Legacy Integration**: RESTful API layer for anatomyland.com integration
- **Hosting**: Cloud provider supporting both main app and legacy integration. AWS Lightsail, Github for code repository.
- **Domain Strategy**: 
  - New app: app.anatomyland.com
  - Legacy/Marketing: anatomyland.com

---

### 3.2. APIs & Integrations

**New Platform APIs:**
- `/health` - Health check
- `/api/anatomy/regions` - Get all 6 anatomy regions with themes
- `/api/anatomy/regions/:regionId` - Get specific region details and content hierarchy
- `/api/user/progress` - Get user progress across all formats
- `/api/content/mini-videos/:regionId` - Returns array of sources with MIME types and provider metadata (e.g., `type: "application/x-mpegURL"`, `provider: "vimeo"`)
- `/api/content/reels/:regionId` - Get reels content from Vimeo
- `/api/quiz/:regionId` - Get region-specific quiz questions
- `/api/vimeo/sync` - Sync video content from Vimeo folders
- `/api/vimeo/video/:videoId/metadata` - Get specific video details from Vimeo

**Vimeo API Integration:**
- **Authentication**: Vimeo API access token for video retrieval
- **Content Discovery**: `/folders` endpoint to map region/lesson structure
- **Video Metadata**: `/videos/{video_id}` for video details and playback URLs
- **Folder Management**: `/folders/{folder_id}/items` for content organization
- **Analytics Integration**: Vimeo analytics for video engagement tracking
- **Embed Configuration**: Custom player settings and branding

**Legacy Integration APIs:**
- `/api/legacy/auth` - Authenticate against anatomyland.com
- `/api/legacy/subscription` - Get user subscription status
- `/api/legacy/progress` - Import existing user progress (**Potential**)
- `/api/legacy/content` - Access full-length video library

**External Integrations:**
- **Video Hosting**: Vimeo Pro/Business account for content delivery
- **Analytics**: Combined Vimeo analytics + custom user behavior tracking
- **Mobile Optimization**: Progressive Web App capabilities with Vimeo streaming

**Player Framework Integration:**
- **Video.js Core** for unified UI/UX
- **Plugins Used**:
  - `videojs-vimeo` for Vimeo playback
  - `videojs-playlist` for sequential reels
  - `videojs-contrib-quality-levels` & `videojs-hls-quality-selector` for bitrate switching
- **CSS/Theme**: Custom SCSS extending Video.js default skin

---

### 3.3. Security & Compliance

- HTTPS encryption for all data transmission
- **Legacy Data Security**: Secure handling of existing user credentials and data
- User data privacy compliance (FERPA for educational content)
- **Vimeo Content Protection**: 
  - Domain-level privacy settings for video access
  - Password protection for sensitive content
  - Custom player domain restrictions
- **API Security**: 
  - Vimeo API token management and rotation
  - Rate limiting for both internal and Vimeo API calls  
  - Secure authentication for legacy integration endpoints
- Session-based authentication with secure token management

---

### 3.4. Performance Assumptions

- **Micro-Learning Optimization**: <1 second load time for reels, <2 seconds for mini-videos
- **Vimeo CDN Performance**: Leverage Vimeo's global CDN for optimal video delivery
- **Mobile-First Performance**: Vimeo's adaptive streaming for mobile consumption
- **Legacy Integration**: <3 seconds response time for legacy data requests
- **Concurrent Users**: Support for 1,000-10,000+ users with Vimeo's scaling infrastructure
- **Progressive Web App**: Offline capability for downloaded mini-content (where Vimeo allows)
- **Content Sync**: Daily automated sync with Vimeo folder structure for new content discovery
- **Video.js Lightweight Footprint**: Lazy-load player scripts/components
- **Provider Agnostic**: Ability to switch CDN or hosting without front-end rewrite
- **Caching**: Service Worker caches Video.js assets & poster images

---

### 3.5. Constraints & Migration Strategy

- **Dual Platform Operation**: Both legacy and new platforms must operate simultaneously
- **User Experience Continuity**: Seamless transition without data loss
- **Vimeo Content Management**: 
  - Business owner assigns content manager for Vimeo organization
  - Folder structure must match application's region/lesson hierarchy
  - Video naming conventions for automated categorization
- **Content Format Migration**: Convert existing long-form content to mini and reel formats in Vimeo
- **Subscription Continuity**: Legacy billing and subscription management integration
- **Mobile-First Approach**: Primary focus on mobile consumption patterns with Vimeo mobile optimization
- **Vimeo Account Requirements**: 
  - Vimeo Pro or Business account for API access and custom players
  - Sufficient storage and bandwidth allocation for educational content
- **Phased Legacy Transition**: 
  - Phase 1: New app launch with Vimeo integration and legacy authentication
  - Phase 2: Migrate core features to new platform with full Vimeo content library
  - Phase 3: Legacy site becomes marketing/landing page only
- **Content Creation Workflow**: 
  - Content manager uploads to Vimeo in structured folders
  - Application automatically discovers and categorizes new content
  - Manual content review and approval process before public availability

---

## 🎥 5. Vimeo Content Management Strategy

### 5.1. Vimeo Account Structure

**Required Vimeo Plan**: Vimeo Pro or Business for API access and advanced features
- Custom player branding
- Advanced privacy controls
- API access for content management
- Analytics and engagement metrics
- Domain-level security restrictions

### 5.2. Content Organization in Vimeo

**Folder Hierarchy:**
```
Anatomy Land V2/
├── Head-And-Neck/
│   ├── Extra-Ocular-Muscles/
│   │   ├── minis/
│   │   └── reels/
│   ├── Facial-Muscles/
│   │   ├── minis/
│   │   └── reels/
├── Thorax/
│   ├── Cardiac-Anatomy/
│   │   ├── minis/
│   │   └── reels/
│   ├── Pulmonary-Anatomy/
│   │   ├── minis/
│   │   └── reels/
├── Upper-Extremity/
│   ├── Brachial-Plexus/
│   │   ├── minis/
│   │   └── reels/
├── Genitourinary/
│   ├── Renal-Anatomy/
│   │   ├── minis/
│   │   └── reels/
├── Lower-Extremity/
│   ├── Knee-Anatomy/
│   │   ├── minis/
│   │   └── reels/
├── Gastrointestinal/
│   ├── Small-Intestine/
│   │   ├── minis/
│   │   └── reels/
```

### 5.3. Content Manager Workflow

**Content Manager Responsibilities:**
1. **Upload Management**: Upload mini-videos and reels to appropriate Vimeo folders
2. **Metadata Management**: Add titles, descriptions, and tags for proper categorization
3. **Quality Control**: Review content before making it publicly available
4. **Organization**: Maintain folder structure consistency for application mapping
5. **Privacy Settings**: Configure appropriate privacy and access controls

**Automated Content Discovery:**
- Application runs daily sync with Vimeo API
- New videos automatically appear in appropriate app sections
- Metadata updates reflected in real-time
- Content approval workflow before student access

### 5.4. Technical Implementation Notes

**Content Mapping Strategy:**
- Folder names map to standard anatomical regions and lesson topics (e.g., `Head-And-Neck/Extra-Ocular-Muscles`)
- Video titles, descriptions, and **tags** (official Vimeo metadata) provide additional categorization (e.g., `tag:mini`, `tag:reel`)
- Automatic thumbnail generation and optimization via Vimeo API

**Investigation Areas:**
- **Folder-to-Content Mapping**: How to reliably map Vimeo folder (project) structure to app navigation using the Vimeo `/me/projects` and `/project/{project_id}/videos` endpoints
- **Content Approval Workflow**: Mechanism to control when new uploads go live (leveraging Vimeo `privacy.view` and `publish` status fields)
- **Metadata Extraction**: Best practices for extracting educational metadata from Vimeo's official fields (`name`, `description`, `tags`, `duration`)
- **Real-time Sync**: Frequency and method for content updates from Vimeo

---
